<?php
// ملف اختبار تحديث العروض مع الحقول الجديدة
require_once 'config.php';

echo "<h1>🧪 اختبار تحديث العروض</h1>";

// اختبار إضافة عرض تجريبي مع الحقول الجديدة
echo "<h2>➕ اختبار إضافة عرض تجريبي:</h2>";

$test_offer = [
    'name' => 'عرض تجريبي - ' . date('Y-m-d H:i:s'),
    'link' => 'https://example.com/test-offer',
    'image' => 'https://via.placeholder.com/300x200/667eea/ffffff?text=Test+Offer',
    'description' => 'هذا وصف تجريبي للعرض. يحتوي على تفاصيل مهمة حول العرض وما يقدمه للمستخدمين. يمكن أن يكون الوصف طويلاً ومفصلاً لشرح جميع مميزات العرض.',
    'additional_terms' => 'شروط إضافية: يجب أن يكون المستخدم فوق 18 سنة. العرض صالح لمدة 30 يوماً فقط. لا يمكن دمج هذا العرض مع عروض أخرى.'
];

if (insertOffer($pdo, $test_offer['name'], $test_offer['link'], $test_offer['image'], $test_offer['description'], $test_offer['additional_terms'])) {
    echo "<p style='color: green;'>✅ تم إضافة العرض التجريبي بنجاح!</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إضافة العرض التجريبي</p>";
}

echo "<hr>";

// عرض جميع العروض مع الحقول الجديدة
echo "<h2>📋 جميع العروض مع الحقول الجديدة:</h2>";

$offers = getAllOffers($pdo);

if (!empty($offers)) {
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-top: 20px;'>";
    
    foreach ($offers as $offer) {
        echo "<div style='border: 1px solid #ddd; padding: 20px; border-radius: 10px; background: white;'>";
        echo "<h3 style='color: #333; margin-bottom: 15px;'>" . htmlspecialchars($offer['name']) . "</h3>";
        
        // عرض الصورة
        echo "<img src='" . htmlspecialchars($offer['image']) . "' style='width: 100%; height: 150px; object-fit: cover; border-radius: 5px; margin-bottom: 15px;' onerror='this.src=\"https://via.placeholder.com/300x150?text=No+Image\"'>";
        
        // عرض الوصف
        if (!empty($offer['description'])) {
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 3px solid #667eea;'>";
            echo "<strong>📝 الوصف:</strong><br>";
            echo "<span style='color: #666; font-size: 14px;'>" . htmlspecialchars($offer['description']) . "</span>";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 8px; border-radius: 5px; margin-bottom: 10px; color: #856404; font-size: 14px;'>";
            echo "⚠️ لا يوجد وصف لهذا العرض";
            echo "</div>";
        }
        
        // عرض الشروط الإضافية
        if (!empty($offer['additional_terms'])) {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 3px solid #ffc107;'>";
            echo "<strong>⚠️ شروط إضافية:</strong><br>";
            echo "<span style='color: #856404; font-size: 13px;'>" . htmlspecialchars($offer['additional_terms']) . "</span>";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 8px; border-radius: 5px; margin-bottom: 10px; color: #155724; font-size: 14px;'>";
            echo "✅ لا توجد شروط إضافية";
            echo "</div>";
        }
        
        // رابط العرض
        echo "<a href='" . htmlspecialchars($offer['link']) . "' target='_blank' style='display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; font-weight: 600; margin-bottom: 10px;'>🔗 مشاهدة العرض</a>";
        
        // تاريخ الإضافة
        echo "<div style='color: #999; font-size: 12px; margin-top: 10px;'>";
        echo "تاريخ الإضافة: " . date('Y-m-d H:i:s', strtotime($offer['created_date']));
        echo "</div>";
        
        echo "</div>";
    }
    
    echo "</div>";
} else {
    echo "<p style='color: orange;'>⚠️ لا توجد عروض حالياً</p>";
}

echo "<hr>";

// اختبار قاعدة البيانات
echo "<h2>🗄️ اختبار هيكل قاعدة البيانات:</h2>";

try {
    $stmt = $pdo->query("DESCRIBE offers");
    $columns = $stmt->fetchAll();
    
    echo "<table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: right;'>اسم العمود</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: right;'>النوع</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: right;'>Null</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: right;'>المفتاح</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: right;'>القيمة الافتراضية</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $column['Key'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color: green; margin-top: 15px;'>✅ هيكل قاعدة البيانات صحيح ويحتوي على الحقول الجديدة</p>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";

// إحصائيات
echo "<h2>📊 إحصائيات العروض:</h2>";

$total_offers = countOffers($pdo);
$offers_with_description = 0;
$offers_with_terms = 0;

foreach ($offers as $offer) {
    if (!empty($offer['description'])) {
        $offers_with_description++;
    }
    if (!empty($offer['additional_terms'])) {
        $offers_with_terms++;
    }
}

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;'>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #1976d2;'>$total_offers</div>";
echo "<div style='color: #666;'>إجمالي العروض</div>";
echo "</div>";

echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #7b1fa2;'>$offers_with_description</div>";
echo "<div style='color: #666;'>عروض مع وصف</div>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #f57c00;'>$offers_with_terms</div>";
echo "<div style='color: #666;'>عروض مع شروط إضافية</div>";
echo "</div>";

$completion_rate = $total_offers > 0 ? round((($offers_with_description + $offers_with_terms) / ($total_offers * 2)) * 100, 1) : 0;

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #2e7d32;'>$completion_rate%</div>";
echo "<div style='color: #666;'>معدل اكتمال البيانات</div>";
echo "</div>";

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - لرؤية العروض مع الحقول الجديدة</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - لإضافة عروض جديدة</p>";
echo "<p><a href='api_offers.php' style='color: #007bff;'>🌐 عروض API</a> - لجلب عروض من CPAlead</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
}
</style>
