<?php
require_once 'config.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 عرض توضيحي لنظام Tooltips البلدان</title>
    <link rel="stylesheet" href="country-tooltips.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        
        .demo-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .demo-section h3 {
            color: #555;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .country-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .country-badge-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 5px;
        }
        
        .offer-card-demo {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
        }
        
        .offer-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .offer-info {
            color: #666;
            margin: 10px 0;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight {
            background: rgba(102, 126, 234, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-top: 4px solid #667eea;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .navigation {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 نظام Tooltips البلدان</h1>
            <p>عرض توضيحي شامل لنظام عرض أسماء البلدان كاملة عند الوقوف على الأسماء المختصرة</p>
        </div>

        <div class="demo-section">
            <h2>🌍 أمثلة تفاعلية</h2>
            
            <h3>1. البلدان في شارة العرض (كما في الصفحة الرئيسية):</h3>
            <div class="country-badge-demo">
                <span class="country-item" title="الولايات المتحدة الأمريكية">US</span>, 
                <span class="country-item" title="المملكة المتحدة">GB</span>, 
                <span class="country-item" title="ألمانيا">DE</span>, 
                <span class="country-item" title="فرنسا">FR</span>, 
                <span class="country-more" title="إيطاليا، إسبانيا، هولندا، بلجيكا، سويسرا">+5 أخرى</span>
            </div>
            
            <h3>2. البلدان في معلومات العرض (كما في صفحة الإدارة):</h3>
            <div class="offer-card-demo">
                <div class="offer-title">عرض تجريبي للاختبار</div>
                <div class="offer-info">
                    <strong>البلدان المستهدفة:</strong> 
                    <span class="country-tooltip" title="الولايات المتحدة الأمريكية">US</span>, 
                    <span class="country-tooltip" title="كندا">CA</span>, 
                    <span class="country-tooltip" title="أستراليا">AU</span>, 
                    <span class="country-tooltip" title="المملكة المتحدة">GB</span>, 
                    <span class="country-more-tooltip" title="ألمانيا، فرنسا، إيطاليا، إسبانيا، هولندا، بلجيكا">+6 أخرى</span>
                </div>
            </div>
            
            <h3>3. البلدان العربية:</h3>
            <div class="country-list">
                <span class="country-tooltip country-arabic" title="المملكة العربية السعودية">SA</span>
                <span class="country-tooltip country-arabic" title="جمهورية مصر العربية">EG</span>
                <span class="country-tooltip country-arabic" title="دولة الإمارات العربية المتحدة">AE</span>
                <span class="country-tooltip country-arabic" title="دولة الكويت">KW</span>
                <span class="country-tooltip country-arabic" title="دولة قطر">QA</span>
                <span class="country-tooltip country-arabic" title="مملكة البحرين">BH</span>
                <span class="country-tooltip country-arabic" title="سلطنة عمان">OM</span>
                <span class="country-tooltip country-arabic" title="المملكة الأردنية الهاشمية">JO</span>
                <span class="country-tooltip country-arabic" title="الجمهورية اللبنانية">LB</span>
            </div>
            
            <h3>4. العروض العالمية:</h3>
            <div class="country-badge-demo">
                <span class="country-item country-global" title="متاح لجميع البلدان في العالم">🌐 جميع البلدان</span>
            </div>
        </div>

        <div class="demo-section">
            <h2>💻 كيفية الاستخدام</h2>
            
            <h3>1. تضمين ملفات CSS و JavaScript:</h3>
            <div class="code-example">
&lt;link rel="stylesheet" href="country-tooltips.css"&gt;
&lt;script src="country-tooltips.js"&gt;&lt;/script&gt;
            </div>
            
            <h3>2. استخدام الكلاسات المناسبة:</h3>
            <div class="code-example">
&lt;!-- للبلدان الفردية --&gt;
&lt;span class="country-tooltip" title="الولايات المتحدة الأمريكية"&gt;US&lt;/span&gt;

&lt;!-- للبلدان الإضافية --&gt;
&lt;span class="country-more-tooltip" title="ألمانيا، فرنسا، إيطاليا"&gt;+3 أخرى&lt;/span&gt;

&lt;!-- في شارة العرض --&gt;
&lt;span class="country-item" title="المملكة المتحدة"&gt;GB&lt;/span&gt;
            </div>
            
            <h3>3. استخدام PHP لتوليد الـ tooltips:</h3>
            <div class="code-example">
&lt;?php
$countries = ['US', 'GB', 'DE', 'FR'];
foreach ($countries as $country) {
    $country_name = getCountryName($country);
    echo '&lt;span class="country-tooltip" title="' . htmlspecialchars($country_name) . '"&gt;' . 
         htmlspecialchars($country) . '&lt;/span&gt;';
}
?&gt;
            </div>
        </div>

        <div class="demo-section">
            <h2>✨ المميزات</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">تصميم جميل</div>
                    <div class="feature-description">
                        tooltips بتصميم عصري مع أنيميشن سلس وألوان متناسقة مع تصميم الموقع
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">متجاوب</div>
                    <div class="feature-description">
                        يعمل بشكل مثالي على جميع الأجهزة والشاشات مع دعم خاص للأجهزة اللمسية
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">أداء عالي</div>
                    <div class="feature-description">
                        محسن للأداء مع استخدام تقنيات حديثة مثل Intersection Observer
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <div class="feature-title">دعم شامل</div>
                    <div class="feature-description">
                        يدعم جميع البلدان مع أسماء باللغة العربية وتصنيفات خاصة
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">سهل التخصيص</div>
                    <div class="feature-description">
                        إعدادات قابلة للتخصيص مع API بسيط للتحكم في السلوك
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">♿</div>
                    <div class="feature-title">إمكانية الوصول</div>
                    <div class="feature-description">
                        متوافق مع معايير الوصولية مع دعم لوحة المفاتيح وقارئات الشاشة
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 إعدادات متقدمة</h2>
            
            <h3>تخصيص الإعدادات عبر JavaScript:</h3>
            <div class="code-example">
// تغيير إعدادات الـ tooltips
CountryTooltips.config.delay = 300;        // تقليل تأخير الظهور
CountryTooltips.config.maxWidth = 300;     // زيادة العرض الأقصى
CountryTooltips.config.position = 'bottom'; // تغيير الموضع

// إعادة تهيئة النظام
CountryTooltips.update();
            </div>
            
            <h3>إضافة tooltips ديناميكياً:</h3>
            <div class="code-example">
// إظهار tooltip يدوياً
const element = document.querySelector('.my-country');
CountryTooltips.show(element, 'الولايات المتحدة الأمريكية');

// إخفاء جميع الـ tooltips
CountryTooltips.hide();
            </div>
        </div>

        <div class="navigation">
            <h2>🔗 الانتقال إلى الصفحات</h2>
            <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
            <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
            <a href="manage_offers.php" class="nav-link">🗂️ إدارة العروض</a>
            <a href="test_homepage_offers.php" class="nav-link">🧪 اختبار العروض</a>
        </div>
    </div>

    <script src="country-tooltips.js"></script>
    <script>
        // تخصيصات إضافية للعرض التوضيحي
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل العرض التوضيحي لنظام tooltips البلدان');
            
            // إضافة تأثيرات خاصة للعرض التوضيحي
            const demoElements = document.querySelectorAll('.country-tooltip, .country-item, .country-more, .country-more-tooltip');
            
            demoElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });
                
                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
