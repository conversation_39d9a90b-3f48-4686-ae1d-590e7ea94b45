<?php
// ملف اختبار نظام Postback
require_once 'config.php';

echo "<h1>🧪 اختبار نظام Postback</h1>";

// جلب إعدادات Postback
$settings = getPostbackSettings($pdo);

echo "<h2>⚙️ إعدادات Postback الحالية:</h2>";
if ($settings) {
    echo "<p><strong>الحالة:</strong> " . ($settings['is_enabled'] ? '✅ مفعل' : '❌ معطل') . "</p>";
    echo "<p><strong>كلمة المرور:</strong> " . (!empty($settings['postback_password']) ? '🔒 محمي بكلمة مرور' : '🔓 بدون كلمة مرور') . "</p>";
    echo "<p><strong>IP المسموح:</strong> " . htmlspecialchars($settings['whitelist_ip']) . "</p>";
    if (!empty($settings['postback_url'])) {
        echo "<p><strong>رابط خارجي:</strong> " . htmlspecialchars($settings['postback_url']) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ لم يتم العثور على إعدادات Postback</p>";
}

echo "<hr>";

// اختبار إرسال postback تجريبي
echo "<h2>🔬 اختبار Postback تجريبي:</h2>";

$test_data = [
    'campaign_id' => 'TEST_123',
    'campaign_name' => 'Test Campaign',
    'subid' => 'test_user_' . time(),
    'subid2' => 'source_website',
    'subid3' => 'campaign_a',
    'payout' => '1.50',
    'ip_address' => '127.0.0.1',
    'gateway_id' => 'gate_456',
    'lead_id' => 'lead_' . time(),
    'country_iso' => 'US',
    'virtual_currency' => '100'
];

// إضافة كلمة المرور إذا كانت مطلوبة
if ($settings && !empty($settings['postback_password'])) {
    $test_data['password'] = $settings['postback_password'];
}

// بناء رابط الاختبار
$postback_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/postback.php';
$test_url = $postback_url . '?' . http_build_query($test_data) . '&debug=1';

echo "<p><strong>رابط الاختبار:</strong></p>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; overflow-x: auto; margin: 10px 0;'>";
echo htmlspecialchars($test_url);
echo "</div>";

echo "<p><strong>البيانات التجريبية:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo json_encode($test_data, JSON_PRETTY_PRINT);
echo "</pre>";

// إرسال طلب الاختبار
echo "<h3>📡 إرسال طلب الاختبار:</h3>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'CPAlead Test Bot'
    ]
]);

$response = @file_get_contents($test_url, false, $context);

if ($response !== false) {
    echo "<p style='color: green;'>✅ تم إرسال الطلب بنجاح!</p>";
    echo "<p><strong>رد الخادم:</strong></p>";
    echo "<pre style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>❌ فشل في إرسال الطلب</p>";
    echo "<p>تحقق من:</p>";
    echo "<ul>";
    echo "<li>تفعيل Postback في الإعدادات</li>";
    echo "<li>صحة كلمة المرور</li>";
    echo "<li>عمل ملف postback.php</li>";
    echo "</ul>";
}

echo "<hr>";

// عرض آخر التحويلات
echo "<h2>📋 آخر التحويلات:</h2>";
$recent_conversions = getAllConversions($pdo);

if (!empty($recent_conversions)) {
    echo "<table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>التاريخ</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Campaign ID</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>SubID</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>المبلغ</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>الدولة</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>الحالة</th>";
    echo "</tr>";
    
    foreach (array_slice($recent_conversions, 0, 10) as $conversion) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . date('Y-m-d H:i', strtotime($conversion['conversion_date'])) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($conversion['campaign_id']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($conversion['subid']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>$" . number_format($conversion['payout'], 2) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($conversion['country_iso']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $conversion['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (count($recent_conversions) > 10) {
        echo "<p style='text-align: center; margin-top: 10px; color: #666;'>";
        echo "عرض أول 10 تحويلات من إجمالي " . count($recent_conversions) . " تحويل";
        echo "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ لا توجد تحويلات حتى الآن</p>";
}

echo "<hr>";

// فحص ملفات السجل
echo "<h2>📄 فحص ملفات السجل:</h2>";

$log_files = [
    'postback_log.txt' => 'سجل جميع الطلبات',
    'postback_success.txt' => 'سجل التحويلات الناجحة',
    'postback_errors.txt' => 'سجل الأخطاء'
];

foreach ($log_files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "<p><strong>$description ($file):</strong> ✅ موجود - الحجم: " . number_format($size) . " بايت - آخر تعديل: $modified</p>";
        
        if ($size > 0 && $size < 10000) { // عرض المحتوى إذا كان الملف صغير
            $content = file_get_contents($file);
            $lines = explode("\n", $content);
            $last_lines = array_slice($lines, -5); // آخر 5 أسطر
            echo "<details style='margin: 10px 0;'>";
            echo "<summary>عرض آخر 5 أسطر</summary>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
            echo htmlspecialchars(implode("\n", $last_lines));
            echo "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p><strong>$description ($file):</strong> ❌ غير موجود</p>";
    }
}

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='postback_admin.php' style='color: #007bff;'>📊 إدارة Postback</a></p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a></p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #ddd;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
}

details {
    background: white;
    padding: 10px;
    border-radius: 5px;
}
</style>
