/**
 * Country Tooltips JavaScript
 * تحسينات JavaScript لنظام tooltips البلدان
 * يوفر تفاعلات محسنة وإدارة ذكية للـ tooltips
 */

(function() {
    'use strict';
    
    // إعدادات الـ tooltips
    const TOOLTIP_CONFIG = {
        delay: 500,           // تأخير ظهور الـ tooltip (بالميلي ثانية)
        hideDelay: 100,       // تأخير إخفاء الـ tooltip
        maxWidth: 250,        // أقصى عرض للـ tooltip
        offset: 10,           // المسافة من العنصر
        animation: 'fade',    // نوع الأنيميشن
        position: 'top'       // موضع الـ tooltip
    };
    
    // متغيرات عامة
    let activeTooltip = null;
    let tooltipTimeout = null;
    let hideTimeout = null;
    
    /**
     * إنشاء tooltip ديناميكي
     */
    function createTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'dynamic-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            line-height: 1.4;
            text-align: center;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transform: translateY(5px);
            transition: all 0.3s ease;
            max-width: ${TOOLTIP_CONFIG.maxWidth}px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        `;
        
        document.body.appendChild(tooltip);
        return tooltip;
    }
    
    /**
     * تحديد موضع الـ tooltip
     */
    function positionTooltip(tooltip, element) {
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        let top = rect.top + scrollTop - tooltipRect.height - TOOLTIP_CONFIG.offset;
        let left = rect.left + scrollLeft + (rect.width / 2) - (tooltipRect.width / 2);
        
        // التأكد من أن الـ tooltip لا يخرج من الشاشة
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        
        // تعديل الموضع الأفقي
        if (left < 10) {
            left = 10;
        } else if (left + tooltipRect.width > windowWidth - 10) {
            left = windowWidth - tooltipRect.width - 10;
        }
        
        // تعديل الموضع العمودي
        if (top < scrollTop + 10) {
            top = rect.bottom + scrollTop + TOOLTIP_CONFIG.offset;
            tooltip.classList.add('tooltip-bottom');
        } else {
            tooltip.classList.remove('tooltip-bottom');
        }
        
        tooltip.style.top = top + 'px';
        tooltip.style.left = left + 'px';
    }
    
    /**
     * إظهار الـ tooltip
     */
    function showTooltip(element, text) {
        // إخفاء أي tooltip نشط
        hideTooltip();
        
        // إنشاء tooltip جديد
        activeTooltip = createTooltip(element, text);
        
        // تحديد الموضع
        requestAnimationFrame(() => {
            positionTooltip(activeTooltip, element);
            
            // إظهار الـ tooltip
            requestAnimationFrame(() => {
                activeTooltip.style.opacity = '1';
                activeTooltip.style.transform = 'translateY(0)';
            });
        });
    }
    
    /**
     * إخفاء الـ tooltip
     */
    function hideTooltip() {
        if (activeTooltip) {
            activeTooltip.style.opacity = '0';
            activeTooltip.style.transform = 'translateY(5px)';
            
            setTimeout(() => {
                if (activeTooltip && activeTooltip.parentNode) {
                    activeTooltip.parentNode.removeChild(activeTooltip);
                }
                activeTooltip = null;
            }, 300);
        }
    }
    
    /**
     * معالج الـ mouseenter
     */
    function handleMouseEnter(event) {
        const element = event.target;
        const title = element.getAttribute('title') || element.getAttribute('data-tooltip');
        
        if (!title) return;
        
        // إلغاء أي timeout سابق
        if (tooltipTimeout) {
            clearTimeout(tooltipTimeout);
        }
        if (hideTimeout) {
            clearTimeout(hideTimeout);
        }
        
        // إظهار الـ tooltip بعد تأخير
        tooltipTimeout = setTimeout(() => {
            showTooltip(element, title);
        }, TOOLTIP_CONFIG.delay);
    }
    
    /**
     * معالج الـ mouseleave
     */
    function handleMouseLeave(event) {
        // إلغاء إظهار الـ tooltip
        if (tooltipTimeout) {
            clearTimeout(tooltipTimeout);
            tooltipTimeout = null;
        }
        
        // إخفاء الـ tooltip بعد تأخير قصير
        hideTimeout = setTimeout(() => {
            hideTooltip();
        }, TOOLTIP_CONFIG.hideDelay);
    }
    
    /**
     * معالج تغيير حجم النافذة
     */
    function handleResize() {
        if (activeTooltip) {
            hideTooltip();
        }
    }
    
    /**
     * معالج التمرير
     */
    function handleScroll() {
        if (activeTooltip) {
            hideTooltip();
        }
    }
    
    /**
     * تهيئة نظام الـ tooltips
     */
    function initTooltips() {
        // البحث عن جميع العناصر التي تحتوي على tooltips
        const tooltipElements = document.querySelectorAll(
            '.country-tooltip, .country-more-tooltip, .country-item, .country-more, [data-tooltip]'
        );
        
        // إضافة معالجات الأحداث
        tooltipElements.forEach(element => {
            // إزالة title الافتراضي لمنع الـ tooltip المدمج
            const title = element.getAttribute('title');
            if (title) {
                element.setAttribute('data-tooltip', title);
                element.removeAttribute('title');
            }
            
            element.addEventListener('mouseenter', handleMouseEnter);
            element.addEventListener('mouseleave', handleMouseLeave);
        });
        
        // إضافة معالجات الأحداث العامة
        window.addEventListener('resize', handleResize);
        window.addEventListener('scroll', handleScroll);
        
        // إخفاء الـ tooltips عند النقر في أي مكان
        document.addEventListener('click', hideTooltip);
        
        console.log(`تم تهيئة نظام tooltips البلدان - ${tooltipElements.length} عنصر`);
    }
    
    /**
     * تحديث الـ tooltips للعناصر الجديدة
     */
    function updateTooltips() {
        // إزالة المعالجات القديمة
        const oldElements = document.querySelectorAll('[data-tooltip-initialized]');
        oldElements.forEach(element => {
            element.removeEventListener('mouseenter', handleMouseEnter);
            element.removeEventListener('mouseleave', handleMouseLeave);
            element.removeAttribute('data-tooltip-initialized');
        });
        
        // إعادة التهيئة
        initTooltips();
    }
    
    /**
     * تحسين الأداء - استخدام Intersection Observer
     */
    function initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (!entry.isIntersecting && activeTooltip) {
                        hideTooltip();
                    }
                });
            });
            
            // مراقبة العناصر التي تحتوي على tooltips
            const tooltipElements = document.querySelectorAll(
                '.country-tooltip, .country-more-tooltip, .country-item, .country-more'
            );
            
            tooltipElements.forEach(element => {
                observer.observe(element);
            });
        }
    }
    
    /**
     * دعم الأجهزة اللمسية
     */
    function initTouchSupport() {
        // التحقق من دعم اللمس
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            // إضافة معالج للمس
            document.addEventListener('touchstart', (event) => {
                const element = event.target.closest('.country-tooltip, .country-more-tooltip, .country-item, .country-more');
                if (element) {
                    const title = element.getAttribute('data-tooltip');
                    if (title) {
                        event.preventDefault();
                        showTooltip(element, title);
                        
                        // إخفاء بعد 3 ثوان
                        setTimeout(hideTooltip, 3000);
                    }
                }
            });
        }
    }
    
    /**
     * API عام للتحكم في الـ tooltips
     */
    window.CountryTooltips = {
        init: initTooltips,
        update: updateTooltips,
        show: showTooltip,
        hide: hideTooltip,
        config: TOOLTIP_CONFIG
    };
    
    // التهيئة التلقائية عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            initTooltips();
            initIntersectionObserver();
            initTouchSupport();
        });
    } else {
        initTooltips();
        initIntersectionObserver();
        initTouchSupport();
    }
    
    // تحديث الـ tooltips عند تغيير المحتوى ديناميكياً
    if ('MutationObserver' in window) {
        const observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            const hasTooltips = node.querySelector && node.querySelector(
                                '.country-tooltip, .country-more-tooltip, .country-item, .country-more'
                            );
                            if (hasTooltips) {
                                shouldUpdate = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldUpdate) {
                setTimeout(updateTooltips, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
})();
