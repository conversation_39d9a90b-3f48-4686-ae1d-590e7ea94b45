<?php
require_once 'config.php';

// التحقق من وجود معرف العرض
if (!isset($_GET['offer_id']) || !is_numeric($_GET['offer_id'])) {
    header('Location: index.php');
    exit;
}

$offer_id = intval($_GET['offer_id']);

// جلب معلومات العرض
try {
    $stmt = $pdo->prepare("SELECT * FROM offers WHERE id = ?");
    $stmt->execute([$offer_id]);
    $offer = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$offer) {
        header('Location: index.php');
        exit;
    }
} catch(PDOException $e) {
    header('Location: index.php');
    exit;
}

// الحصول على معلومات المستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// الحصول على بلد المستخدم (اختياري)
$country_code = '';
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";

$context = stream_context_create([
    'http' => [
        'timeout' => 5, // مهلة قصيرة لعدم تأخير المستخدم
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);
if ($response !== false) {
    $ip_data = json_decode($response, true);
    if ($ip_data && isset($ip_data['success']) && $ip_data['success']) {
        $country_code = $ip_data['country_code'] ?? '';
    }
}

// تسجيل النقرة
recordClick($pdo, $offer_id, $user_ip, $user_agent, $country_code);

// إعادة توجيه المستخدم للعرض
header('Location: ' . $offer['link']);
exit;
?>
