<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

// التحقق من وجود معرف العرض
if (!isset($_GET['offer_id']) || !is_numeric($_GET['offer_id'])) {
    // تسجيل الخطأ
    error_log("Click Handler: معرف العرض مفقود أو غير صحيح - " . ($_GET['offer_id'] ?? 'غير موجود'));
    header('Location: index.php?error=invalid_offer');
    exit;
}

$offer_id = intval($_GET['offer_id']);

// جلب معلومات العرض
try {
    $stmt = $pdo->prepare("SELECT * FROM offers WHERE id = ?");
    $stmt->execute([$offer_id]);
    $offer = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$offer) {
        error_log("Click Handler: العرض غير موجود - ID: $offer_id");
        header('Location: index.php?error=offer_not_found');
        exit;
    }

    // التحقق من صحة رابط العرض
    if (empty($offer['link']) || !filter_var($offer['link'], FILTER_VALIDATE_URL)) {
        error_log("Click Handler: رابط العرض غير صحيح - ID: $offer_id, Link: " . ($offer['link'] ?? 'فارغ'));
        header('Location: index.php?error=invalid_link');
        exit;
    }

} catch(PDOException $e) {
    error_log("Click Handler: خطأ في قاعدة البيانات - " . $e->getMessage());
    header('Location: index.php?error=database_error');
    exit;
}

// الحصول على معلومات المستخدم
$user_ip = getUserIP();
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// الحصول على بلد المستخدم (اختياري)
$country_code = '';
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";

$context = stream_context_create([
    'http' => [
        'timeout' => 5, // مهلة قصيرة لعدم تأخير المستخدم
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);
if ($response !== false) {
    $ip_data = json_decode($response, true);
    if ($ip_data && isset($ip_data['success']) && $ip_data['success']) {
        $country_code = $ip_data['country_code'] ?? '';
    }
}

// فحص نظام منع التكرار
if (function_exists('isIPBlocked') && isIPBlocked($pdo, $offer_id, $user_ip)) {
    error_log("Click Handler: IP محجوب - IP: $user_ip, Offer ID: $offer_id");
    header('Location: index.php?error=ip_blocked');
    exit;
}

// تسجيل النقرة
try {
    $click_recorded = recordClick($pdo, $offer_id, $user_ip, $user_agent, $country_code);
    if (!$click_recorded) {
        error_log("Click Handler: فشل في تسجيل النقرة - Offer ID: $offer_id, IP: $user_ip");
        // لا نوقف العملية، فقط نسجل الخطأ
    }
} catch (Exception $e) {
    error_log("Click Handler: خطأ في تسجيل النقرة - " . $e->getMessage());
    // لا نوقف العملية، فقط نسجل الخطأ
}

// التحقق من أن الـ headers لم يتم إرسالها
if (headers_sent($file, $line)) {
    error_log("Click Handler: Headers تم إرسالها مسبقاً في $file:$line");
    echo "<script>window.location.href = '" . htmlspecialchars($offer['link']) . "';</script>";
    echo "<meta http-equiv='refresh' content='0;url=" . htmlspecialchars($offer['link']) . "'>";
    echo "<p>إذا لم يتم التحويل تلقائياً، <a href='" . htmlspecialchars($offer['link']) . "'>انقر هنا</a></p>";
    exit;
}

// إعادة توجيه المستخدم للعرض
header('Location: ' . $offer['link']);
exit;
?>
