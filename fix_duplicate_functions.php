<?php
// ملف إصلاح الدوال المكررة
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح الدوال المكررة</h1>";

// قائمة الملفات للفحص
$files_to_check = [
    'config.php',
    'ip_check.php', 
    'click_handler.php',
    'index.php',
    'admin.php',
    'manage_offers.php',
    'api_offers.php',
    'test_ip_check.php',
    'fix_ip_check.php',
    'test_click_handler.php'
];

// الدوال التي يجب أن تكون معرفة في config.php فقط
$core_functions = [
    'getUserIP',
    'getCountryName', 
    'recordClick',
    'isIPBlocked',
    'getClickStats',
    'getRecentClicks',
    'insertOffer',
    'getAllOffers',
    'getOffersByCountry',
    'updateOfferStatus',
    'getOffersByStatus',
    'getOffersStatusStats'
];

echo "<h2>1️⃣ فحص الدوال المكررة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

$duplicates_found = [];
$function_locations = [];

foreach ($files_to_check as $file) {
    if (!file_exists($file)) {
        echo "⚠️ الملف $file غير موجود<br>";
        continue;
    }
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "❌ لا يمكن قراءة الملف $file<br>";
        continue;
    }
    
    // البحث عن تعاريف الدوال
    foreach ($core_functions as $function) {
        if (preg_match('/function\s+' . preg_quote($function) . '\s*\(/i', $content)) {
            if (!isset($function_locations[$function])) {
                $function_locations[$function] = [];
            }
            $function_locations[$function][] = $file;
        }
    }
}

// عرض النتائج
foreach ($function_locations as $function => $files) {
    if (count($files) > 1) {
        $duplicates_found[$function] = $files;
        echo "❌ <strong>$function</strong> معرفة في: " . implode(', ', $files) . "<br>";
    } else {
        echo "✅ <strong>$function</strong> معرفة في: " . implode(', ', $files) . "<br>";
    }
}

if (empty($duplicates_found)) {
    echo "<br>🎉 <strong>لا توجد دوال مكررة!</strong><br>";
} else {
    echo "<br>⚠️ <strong>تم العثور على " . count($duplicates_found) . " دالة مكررة</strong><br>";
}

echo "</div>";

// إصلاح الدوال المكررة
if (!empty($duplicates_found)) {
    echo "<h2>2️⃣ إصلاح الدوال المكررة:</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";
    
    $fixes_applied = 0;
    
    foreach ($duplicates_found as $function => $files) {
        echo "<strong>🔧 إصلاح $function:</strong><br>";
        
        // الاحتفاظ بالتعريف في config.php فقط
        foreach ($files as $file) {
            if ($file === 'config.php') {
                echo "✅ الاحتفاظ بالتعريف في $file<br>";
                continue;
            }
            
            // حذف التعريف من الملفات الأخرى
            $content = file_get_contents($file);
            if ($content === false) {
                echo "❌ لا يمكن قراءة $file<br>";
                continue;
            }
            
            // البحث عن تعريف الدالة وحذفه
            $pattern = '/\/\/[^\n]*\n\s*function\s+' . preg_quote($function) . '\s*\([^}]*\}\s*\n?/s';
            $new_content = preg_replace($pattern, "// $function معرفة في config.php\n", $content);
            
            if ($new_content !== $content) {
                if (file_put_contents($file, $new_content)) {
                    echo "✅ تم حذف التعريف من $file<br>";
                    $fixes_applied++;
                } else {
                    echo "❌ فشل في كتابة $file<br>";
                }
            } else {
                echo "⚠️ لم يتم العثور على التعريف في $file للحذف<br>";
            }
        }
        echo "<br>";
    }
    
    echo "<strong>🎉 تم تطبيق $fixes_applied إصلاح</strong><br>";
    echo "</div>";
}

// فحص الأخطاء الأخرى
echo "<h2>3️⃣ فحص أخطاء PHP الأخرى:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";

foreach ($files_to_check as $file) {
    if (!file_exists($file)) {
        continue;
    }
    
    // فحص syntax
    $output = [];
    $return_var = 0;
    exec("php -l \"$file\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "✅ $file - لا توجد أخطاء syntax<br>";
    } else {
        echo "❌ $file - أخطاء syntax:<br>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; margin: 5px 0;'>";
        echo htmlspecialchars(implode("\n", $output));
        echo "</pre>";
    }
}

echo "</div>";

// نصائح الوقاية
echo "<h2>4️⃣ نصائح لتجنب الدوال المكررة:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";

echo "<strong>📋 أفضل الممارسات:</strong><br>";
echo "1. <strong>config.php</strong> - ضع جميع الدوال الأساسية هنا<br>";
echo "2. <strong>require_once</strong> - استخدم require_once بدلاً من require<br>";
echo "3. <strong>function_exists()</strong> - فحص وجود الدالة قبل تعريفها<br>";
echo "4. <strong>namespace</strong> - استخدم namespaces للدوال المتخصصة<br>";
echo "5. <strong>class</strong> - اجمع الدوال المترابطة في classes<br><br>";

echo "<strong>🔍 فحص دوري:</strong><br>";
echo "- شغل هذا الملف بانتظام للفحص<br>";
echo "- استخدم أدوات IDE للتحقق من التعاريف<br>";
echo "- اختبر الملفات بعد كل تعديل<br><br>";

echo "<strong>🛠️ إصلاح سريع:</strong><br>";
echo "إذا واجهت خطأ 'Cannot redeclare function':<br>";
echo "1. ابحث عن الدالة في جميع الملفات<br>";
echo "2. احتفظ بتعريف واحد فقط (عادة في config.php)<br>";
echo "3. استبدل التعاريف الأخرى بتعليقات<br>";
echo "4. تأكد من تضمين config.php في جميع الملفات<br>";

echo "</div>";

// اختبار سريع
echo "<h2>5️⃣ اختبار سريع:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";

echo "<strong>🧪 اختبار الدوال الأساسية:</strong><br>";

// اختبار تضمين config.php
try {
    if (!function_exists('getUserIP')) {
        require_once 'config.php';
    }
    
    if (function_exists('getUserIP')) {
        $test_ip = getUserIP();
        echo "✅ getUserIP() تعمل: $test_ip<br>";
    } else {
        echo "❌ getUserIP() غير متوفرة<br>";
    }
    
    if (function_exists('getCountryName')) {
        $test_country = getCountryName('US');
        echo "✅ getCountryName() تعمل: $test_country<br>";
    } else {
        echo "❌ getCountryName() غير متوفرة<br>";
    }
    
    // اختبار قاعدة البيانات
    if (isset($pdo)) {
        echo "✅ اتصال قاعدة البيانات متوفر<br>";
        
        if (function_exists('getAllOffers')) {
            $offers = getAllOffers($pdo);
            echo "✅ getAllOffers() تعمل: " . count($offers) . " عرض<br>";
        } else {
            echo "❌ getAllOffers() غير متوفرة<br>";
        }
    } else {
        echo "❌ اتصال قاعدة البيانات غير متوفر<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='ip_check.php' style='color: #007bff;'>🔍 صفحة فحص IP</a> - اختبار الصفحة المصلحة</p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - اختبار الموقع</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - فحص النظام العام</p>";

// معلومات النظام
echo "<hr>";
echo "<h2>ℹ️ معلومات النظام:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 14px;'>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Memory Usage:</strong> " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB<br>";
echo "<strong>Peak Memory:</strong> " . number_format(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB<br>";
echo "<strong>Execution Time:</strong> " . number_format(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 3) . " seconds<br>";
echo "<strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
}

strong {
    color: #333;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
