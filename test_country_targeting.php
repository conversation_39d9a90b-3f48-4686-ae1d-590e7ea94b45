<?php
// ملف اختبار نظام استهداف البلدان
require_once 'config.php';

echo "<h1>🌍 اختبار نظام استهداف البلدان</h1>";

// الحصول على IP المستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';

echo "<h2>📋 معلومات المستخدم:</h2>";
echo "<p><strong>IP:</strong> $user_ip</p>";

// فحص بلد المستخدم
$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);
$user_country = null;

if ($response !== false) {
    $ip_data = json_decode($response, true);
    if ($ip_data && isset($ip_data['success']) && $ip_data['success']) {
        $user_country = $ip_data['country_code'] ?? null;
        echo "<p><strong>البلد:</strong> " . getCountryName($user_country) . " ($user_country)</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في الحصول على بيانات البلد</p>";
    }
} else {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بـ API</p>";
}

echo "<hr>";

// إضافة عروض تجريبية
echo "<h2>➕ إضافة عروض تجريبية:</h2>";

$test_offers = [
    [
        'name' => 'عرض للولايات المتحدة',
        'link' => 'https://example.com/us-offer',
        'image' => 'https://via.placeholder.com/300x200/0066cc/ffffff?text=US+Offer',
        'description' => 'عرض مخصص للمستخدمين في الولايات المتحدة',
        'terms' => 'متاح فقط للمقيمين في الولايات المتحدة',
        'countries' => 'US'
    ],
    [
        'name' => 'عرض للمملكة المتحدة وكندا',
        'link' => 'https://example.com/uk-ca-offer',
        'image' => 'https://via.placeholder.com/300x200/cc0066/ffffff?text=UK+CA+Offer',
        'description' => 'عرض مخصص للمستخدمين في المملكة المتحدة وكندا',
        'terms' => 'متاح للمقيمين في المملكة المتحدة وكندا فقط',
        'countries' => 'GB, CA'
    ],
    [
        'name' => 'عرض عالمي',
        'link' => 'https://example.com/global-offer',
        'image' => 'https://via.placeholder.com/300x200/00cc66/ffffff?text=Global+Offer',
        'description' => 'عرض متاح لجميع البلدان',
        'terms' => 'متاح عالمياً',
        'countries' => 'ALL'
    ],
    [
        'name' => 'عرض بدون استهداف',
        'link' => 'https://example.com/no-targeting',
        'image' => 'https://via.placeholder.com/300x200/666666/ffffff?text=No+Targeting',
        'description' => 'عرض بدون استهداف جغرافي محدد',
        'terms' => 'بدون قيود جغرافية',
        'countries' => ''
    ]
];

$added_offers = 0;
foreach ($test_offers as $offer) {
    if (insertOffer($pdo, $offer['name'], $offer['link'], $offer['image'], $offer['description'], $offer['terms'], $offer['countries'])) {
        $added_offers++;
        echo "<p style='color: green;'>✅ تم إضافة: " . $offer['name'] . " (البلدان: " . ($offer['countries'] ?: 'غير محدد') . ")</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة: " . $offer['name'] . "</p>";
    }
}

echo "<p><strong>تم إضافة $added_offers عرض تجريبي</strong></p>";

echo "<hr>";

// اختبار جلب العروض حسب البلد
echo "<h2>🔍 اختبار جلب العروض حسب البلد:</h2>";

// جلب جميع العروض
$all_offers = getAllOffers($pdo);
echo "<h3>📋 جميع العروض (" . count($all_offers) . "):</h3>";
foreach ($all_offers as $offer) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
    echo "<strong>" . htmlspecialchars($offer['name']) . "</strong><br>";
    echo "<small>البلدان: " . ($offer['target_countries'] ?: 'غير محدد') . "</small>";
    echo "</div>";
}

// جلب العروض حسب بلد المستخدم
if ($user_country) {
    $country_offers = getOffersByCountry($pdo, $user_country);
    echo "<h3>🎯 العروض المناسبة لبلدك ($user_country) - " . count($country_offers) . " عرض:</h3>";
    foreach ($country_offers as $offer) {
        echo "<div style='border: 1px solid #28a745; padding: 10px; margin: 5px 0; border-radius: 5px; background: #f8fff8;'>";
        echo "<strong>" . htmlspecialchars($offer['name']) . "</strong><br>";
        echo "<small>البلدان: " . ($offer['target_countries'] ?: 'غير محدد') . "</small>";
        echo "</div>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ لا يمكن تحديد بلدك لاختبار الفلترة</p>";
}

echo "<hr>";

// اختبار بلدان مختلفة
echo "<h2>🌍 اختبار بلدان مختلفة:</h2>";

$test_countries = ['US', 'GB', 'CA', 'DE', 'FR'];

foreach ($test_countries as $country) {
    $offers = getOffersByCountry($pdo, $country);
    echo "<h4>$country (" . getCountryName($country) . ") - " . count($offers) . " عرض:</h4>";
    echo "<ul>";
    foreach ($offers as $offer) {
        echo "<li>" . htmlspecialchars($offer['name']) . " (البلدان: " . ($offer['target_countries'] ?: 'غير محدد') . ")</li>";
    }
    echo "</ul>";
}

echo "<hr>";

// إحصائيات
echo "<h2>📊 إحصائيات النظام:</h2>";

$total_offers = count($all_offers);
$offers_with_targeting = 0;
$offers_global = 0;
$offers_no_targeting = 0;

foreach ($all_offers as $offer) {
    if (empty($offer['target_countries'])) {
        $offers_no_targeting++;
    } elseif (strpos($offer['target_countries'], 'ALL') !== false) {
        $offers_global++;
    } else {
        $offers_with_targeting++;
    }
}

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;'>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #1976d2;'>$total_offers</div>";
echo "<div style='color: #666;'>إجمالي العروض</div>";
echo "</div>";

echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #7b1fa2;'>$offers_with_targeting</div>";
echo "<div style='color: #666;'>عروض مستهدفة</div>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #2e7d32;'>$offers_global</div>";
echo "<div style='color: #666;'>عروض عالمية</div>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #f57c00;'>$offers_no_targeting</div>";
echo "<div style='color: #666;'>بدون استهداف</div>";
echo "</div>";

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - لرؤية العروض المفلترة</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - لإضافة عروض جديدة</p>";
echo "<p><a href='api_offers.php' style='color: #007bff;'>🌐 عروض API</a> - لجلب عروض من CPAlead</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}
</style>
