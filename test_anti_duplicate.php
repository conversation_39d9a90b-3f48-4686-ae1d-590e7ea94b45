<?php
// ملف اختبار نظام منع التكرار
require_once 'config.php';

echo "<h1>🛡️ اختبار نظام منع التكرار</h1>";

// الحصول على IP المستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();

echo "<h2>📋 معلومات الاختبار:</h2>";
echo "<p><strong>IP الحالي:</strong> $user_ip</p>";

// جلب الإعدادات الحالية
$settings = getAntiDuplicateSettings($pdo);
echo "<h3>⚙️ الإعدادات الحالية:</h3>";
echo "<ul>";
echo "<li><strong>الحالة:</strong> " . ($settings['is_enabled'] ? '✅ مفعل' : '❌ معطل') . "</li>";
echo "<li><strong>مدة المنع:</strong> {$settings['block_duration_days']} يوم</li>";
echo "<li><strong>نوع المنع:</strong> " . ($settings['block_type'] === 'per_offer' ? 'لكل عرض منفصل' : 'منع عالمي') . "</li>";
echo "<li><strong>القائمة البيضاء:</strong> " . ($settings['whitelist_ips'] ?: 'فارغة') . "</li>";
echo "</ul>";

echo "<hr>";

// جلب العروض المتاحة
$offers = getAllOffers($pdo);
echo "<h2>🎯 اختبار العروض المتاحة:</h2>";

if (empty($offers)) {
    echo "<p style='color: red;'>❌ لا توجد عروض متاحة للاختبار</p>";
    echo "<p><a href='admin.php'>إضافة عروض من لوحة التحكم</a></p>";
} else {
    echo "<p>العروض المتاحة: " . count($offers) . "</p>";
    
    foreach ($offers as $offer) {
        $is_blocked = isIPBlocked($pdo, $offer['id'], $user_ip);
        
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: " . ($is_blocked ? '#ffebee' : '#e8f5e8') . ";'>";
        echo "<h4>" . htmlspecialchars($offer['name']) . " (ID: {$offer['id']})</h4>";
        echo "<p><strong>الحالة:</strong> " . ($is_blocked ? '🚫 محجوب' : '✅ متاح') . "</p>";
        
        if (!$is_blocked) {
            echo "<p><a href='click_handler.php?offer_id={$offer['id']}' target='_blank' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;'>🔗 اختبار النقر</a></p>";
        } else {
            echo "<p style='color: #666;'>لا يمكن النقر - محجوب بواسطة نظام منع التكرار</p>";
        }
        echo "</div>";
    }
}

echo "<hr>";

// إحصائيات النقرات
$stats = getClickStats($pdo);
echo "<h2>📊 إحصائيات النقرات:</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #1976d2;'>" . number_format($stats['total_clicks']) . "</div>";
echo "<div style='color: #666;'>إجمالي النقرات</div>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #2e7d32;'>" . number_format($stats['today_clicks']) . "</div>";
echo "<div style='color: #666;'>نقرات اليوم</div>";
echo "</div>";

echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #7b1fa2;'>" . number_format($stats['unique_ips']) . "</div>";
echo "<div style='color: #666;'>IPs فريدة</div>";
echo "</div>";

echo "<div style='background: #ffebee; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #c62828;'>" . number_format($stats['blocked_clicks']) . "</div>";
echo "<div style='color: #666;'>نقرات محجوبة</div>";
echo "</div>";

echo "</div>";

echo "<hr>";

// سجل النقرات الأخيرة
$recent_clicks = getRecentClicks($pdo, 10);
echo "<h2>📋 آخر النقرات:</h2>";

if (empty($recent_clicks)) {
    echo "<p>لا توجد نقرات مسجلة بعد</p>";
} else {
    echo "<table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
    echo "<thead>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 12px; text-align: right; border: 1px solid #ddd;'>التاريخ</th>";
    echo "<th style='padding: 12px; text-align: right; border: 1px solid #ddd;'>العرض</th>";
    echo "<th style='padding: 12px; text-align: right; border: 1px solid #ddd;'>IP</th>";
    echo "<th style='padding: 12px; text-align: right; border: 1px solid #ddd;'>البلد</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($recent_clicks as $click) {
        $is_current_ip = ($click['ip_address'] === $user_ip);
        echo "<tr style='background: " . ($is_current_ip ? '#fff3cd' : 'white') . ";'>";
        echo "<td style='padding: 12px; border: 1px solid #ddd;'>" . date('Y-m-d H:i', strtotime($click['click_date'])) . "</td>";
        echo "<td style='padding: 12px; border: 1px solid #ddd;'>" . htmlspecialchars($click['offer_name'] ?: 'عرض محذوف') . "</td>";
        echo "<td style='padding: 12px; border: 1px solid #ddd; font-family: monospace;'>" . htmlspecialchars($click['ip_address']) . ($is_current_ip ? ' (أنت)' : '') . "</td>";
        echo "<td style='padding: 12px; border: 1px solid #ddd;'>" . htmlspecialchars($click['country_code'] ?: '-') . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
}

echo "<hr>";

// اختبارات متقدمة
echo "<h2>🧪 اختبارات متقدمة:</h2>";

// اختبار القائمة البيضاء
if (!empty($settings['whitelist_ips'])) {
    $whitelist = array_map('trim', explode(',', $settings['whitelist_ips']));
    $is_whitelisted = in_array($user_ip, $whitelist);
    
    echo "<h3>📋 اختبار القائمة البيضاء:</h3>";
    echo "<p><strong>IP الحالي في القائمة البيضاء:</strong> " . ($is_whitelisted ? '✅ نعم' : '❌ لا') . "</p>";
    echo "<p><strong>القائمة البيضاء:</strong> " . htmlspecialchars($settings['whitelist_ips']) . "</p>";
}

// اختبار أنواع المنع
echo "<h3>🎯 اختبار أنواع المنع:</h3>";
if ($settings['block_type'] === 'per_offer') {
    echo "<p>✅ <strong>المنع لكل عرض منفصل:</strong> يمكن للمستخدم النقر على عروض أخرى بعد النقر على عرض واحد</p>";
} else {
    echo "<p>🚫 <strong>المنع العالمي:</strong> يتم منع المستخدم من جميع العروض بعد النقر على أي عرض</p>";
}

echo "<hr>";

// أدوات الاختبار
echo "<h2>🔧 أدوات الاختبار:</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h4>🔄 إعادة تعيين الاختبار</h4>";
echo "<p>حذف جميع نقرات IP الحالي لإعادة الاختبار</p>";
echo "<form method='POST'>";
echo "<button type='submit' name='reset_ip' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"هل أنت متأكد؟\")'>🗑️ حذف نقراتي</button>";
echo "</form>";
echo "</div>";

echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h4>📊 تحديث الإحصائيات</h4>";
echo "<p>إعادة تحميل الصفحة لرؤية أحدث البيانات</p>";
echo "<button onclick='location.reload()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔄 تحديث</button>";
echo "</div>";

echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h4>⚙️ إدارة النظام</h4>";
echo "<p>الانتقال لصفحة إدارة نظام منع التكرار</p>";
echo "<a href='anti_duplicate_admin.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>🛡️ إدارة النظام</a>";
echo "</div>";

echo "</div>";

// معالجة إعادة تعيين IP
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_ip'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM click_tracking WHERE ip_address = ?");
        if ($stmt->execute([$user_ip])) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
            echo "✅ تم حذف جميع نقرات IP الحالي بنجاح!";
            echo "</div>";
            echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
            echo "❌ فشل في حذف النقرات!";
            echo "</div>";
        }
    } catch(PDOException $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
        echo "</div>";
    }
}

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - لاختبار النظام من منظور المستخدم</p>";
echo "<p><a href='anti_duplicate_admin.php' style='color: #007bff;'>🛡️ إدارة منع التكرار</a> - لتغيير إعدادات النظام</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - لإدارة العروض والموقع</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
}
</style>
