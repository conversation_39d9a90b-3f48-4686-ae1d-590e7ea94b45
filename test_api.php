<?php
// ملف اختبار CPAlead API
$cpalead_id = '1941213';

echo "<h1>🧪 اختبار CPAlead API</h1>";

// اختبار جلب العروض
echo "<h2>📋 اختبار جلب العروض:</h2>";

$api_url = "https://www.cpalead.com/api/offers?id=$cpalead_id&country=US&device=desktop&limit=5";

echo "<p><strong>رابط API:</strong> $api_url</p>";

$context = stream_context_create([
    'http' => [
        'timeout' => 15,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);

if ($response !== false) {
    echo "<p style='color: green;'>✅ تم الاتصال بـ API بنجاح!</p>";
    
    $data = json_decode($response, true);
    
    if ($data && isset($data['status']) && $data['status'] === 'success') {
        echo "<p style='color: green;'>✅ تم تحليل البيانات بنجاح!</p>";
        echo "<p><strong>عدد العروض:</strong> " . ($data['number_offers'] ?? 0) . "</p>";
        echo "<p><strong>الدولة:</strong> " . ($data['country'] ?? 'غير محدد') . "</p>";
        
        if (!empty($data['offers'])) {
            echo "<h3>🎯 أول 3 عروض:</h3>";
            foreach (array_slice($data['offers'], 0, 3) as $index => $offer) {
                echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
                echo "<h4>" . ($index + 1) . ". " . htmlspecialchars($offer['title']) . "</h4>";
                echo "<p><strong>المبلغ:</strong> $" . number_format($offer['amount'], 2) . " " . $offer['payout_type'] . "</p>";
                echo "<p><strong>الجهاز:</strong> " . ucfirst($offer['device']) . "</p>";
                echo "<p><strong>الدول:</strong> " . implode(', ', array_slice($offer['countries'], 0, 5)) . "</p>";
                echo "<p><strong>الصورة:</strong> " . (isset($offer['creatives']['url']) ? '✅ متوفرة' : '❌ غير متوفرة') . "</p>";
                if (isset($offer['creatives']['url'])) {
                    echo "<img src='" . htmlspecialchars($offer['creatives']['url']) . "' style='max-width: 200px; height: auto; border-radius: 5px;' onerror='this.style.display=\"none\"'>";
                }
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد عروض متاحة حالياً</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ خطأ في البيانات المستلمة من API</p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
    
} else {
    echo "<p style='color: red;'>❌ فشل في الاتصال بـ API</p>";
    echo "<p>الأسباب المحتملة:</p>";
    echo "<ul>";
    echo "<li>مشكلة في الاتصال بالإنترنت</li>";
    echo "<li>خطأ في معرف API</li>";
    echo "<li>مشكلة في خادم CPAlead</li>";
    echo "</ul>";
}

echo "<hr>";

// اختبار معاملات مختلفة
echo "<h2>🔧 اختبار معاملات مختلفة:</h2>";

$test_params = [
    ['country' => 'user', 'device' => 'user', 'limit' => 10],
    ['country' => 'US', 'device' => 'mobile', 'limit' => 5],
    ['country' => 'GB', 'device' => 'ios', 'limit' => 3, 'type' => 'cpi'],
];

foreach ($test_params as $index => $params) {
    echo "<h3>اختبار " . ($index + 1) . ":</h3>";
    
    $test_url = "https://www.cpalead.com/api/offers?id=$cpalead_id";
    foreach ($params as $key => $value) {
        $test_url .= "&$key=$value";
    }
    
    echo "<p><strong>المعاملات:</strong> " . json_encode($params) . "</p>";
    
    $test_response = @file_get_contents($test_url, false, $context);
    
    if ($test_response !== false) {
        $test_data = json_decode($test_response, true);
        if ($test_data && isset($test_data['status']) && $test_data['status'] === 'success') {
            echo "<p style='color: green;'>✅ نجح - عدد العروض: " . ($test_data['number_offers'] ?? 0) . "</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في تحليل البيانات</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في الاتصال</p>";
    }
}

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='api_offers.php' style='color: #007bff;'>🌐 صفحة إدارة عروض API</a></p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a></p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #ddd;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}
</style>
