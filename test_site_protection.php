<?php
// ملف اختبار نظام حماية الموقع
session_start();
require_once 'config.php';

echo "<h1>🔑 اختبار نظام كلمة مرور الموقع</h1>";

$user_ip = getUserIP();

echo "<h2>📋 معلومات الاختبار:</h2>";
echo "<p><strong>IP الحالي:</strong> $user_ip</p>";
echo "<p><strong>جلسة فتح القفل:</strong> " . (isset($_SESSION['site_unlocked']) && $_SESSION['site_unlocked'] ? '✅ مفتوح' : '❌ مقفل') . "</p>";

// جلب الإعدادات الحالية
$settings = getSiteProtectionSettings($pdo);
echo "<h3>⚙️ الإعدادات الحالية:</h3>";
echo "<ul>";
echo "<li><strong>حالة الموقع:</strong> " . ($settings['is_locked'] ? '🔑 محمي بكلمة مرور' : '🌐 مفتوح للجميع') . "</li>";
echo "<li><strong>كلمة المرور:</strong> " . (!empty($settings['password_hash']) ? '✅ محددة' : '❌ غير محددة') . "</li>";
echo "<li><strong>رسالة القفل:</strong> " . htmlspecialchars($settings['lock_message']) . "</li>";
echo "<li><strong>IPs مسموحة:</strong> " . ($settings['allowed_ips'] ?: 'لا يوجد') . "</li>";
echo "</ul>";

echo "<hr>";

// اختبار الدوال
echo "<h2>🧪 اختبار الدوال:</h2>";

echo "<h3>🔍 فحص حالة الموقع:</h3>";
$is_locked = isSiteLocked($pdo);
echo "<p><strong>isSiteLocked():</strong> " . ($is_locked ? '🔑 محمي بكلمة مرور' : '🌐 مفتوح للجميع') . "</p>";

echo "<h3>🌐 فحص IP المسموح:</h3>";
$is_ip_allowed = isIPAllowed($pdo, $user_ip);
echo "<p><strong>isIPAllowed('$user_ip'):</strong> " . ($is_ip_allowed ? '✅ مسموح' : '❌ غير مسموح') . "</p>";

// اختبار IPs مختلفة
$test_ips = ['127.0.0.1', '***********', '********', '*******'];
echo "<h4>اختبار IPs مختلفة:</h4>";
echo "<ul>";
foreach ($test_ips as $test_ip) {
    $allowed = isIPAllowed($pdo, $test_ip);
    echo "<li><strong>$test_ip:</strong> " . ($allowed ? '✅ مسموح' : '❌ غير مسموح') . "</li>";
}
echo "</ul>";

echo "<hr>";

// محاكاة سيناريوهات مختلفة
echo "<h2>🎭 محاكاة السيناريوهات:</h2>";

echo "<h3>📊 السيناريو الحالي:</h3>";
if (!$is_locked) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";
    echo "🌐 <strong>الموقع مفتوح للجميع:</strong> جميع المستخدمين يمكنهم الوصول مباشرة";
    echo "</div>";
} else {
    if ($is_ip_allowed) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";
        echo "🔑 <strong>الموقع محمي لكن IP مسموح:</strong> يمكنك الوصول بدون كلمة مرور";
        echo "</div>";
    } else {
        if (isset($_SESSION['site_unlocked']) && $_SESSION['site_unlocked']) {
            echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";
            echo "🚀 <strong>تم الدخول بكلمة المرور:</strong> يمكنك تصفح الموقع بحرية";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
            echo "🔑 <strong>الموقع محمي بكلمة مرور:</strong> ستظهر صفحة تسجيل الدخول";
            echo "</div>";
        }
    }
}

echo "<hr>";

// أدوات الاختبار
echo "<h2>🔧 أدوات الاختبار:</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0;'>";

// اختبار كلمة المرور
if (!empty($settings['password_hash'])) {
    echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
    echo "<h4>🔑 اختبار كلمة المرور</h4>";
    echo "<form method='POST'>";
    echo "<input type='password' name='test_password' placeholder='أدخل كلمة المرور...' style='width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<button type='submit' name='test_pwd' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>اختبار</button>";
    echo "</form>";
    
    if (isset($_POST['test_pwd'])) {
        $test_password = $_POST['test_password'];
        $is_valid = verifyUnlockPassword($pdo, $test_password);
        echo "<div style='margin-top: 10px; padding: 10px; border-radius: 5px; background: " . ($is_valid ? '#d4edda' : '#f8d7da') . "; color: " . ($is_valid ? '#155724' : '#721c24') . ";'>";
        echo $is_valid ? '✅ كلمة المرور صحيحة' : '❌ كلمة المرور خاطئة';
        echo "</div>";
    }
    echo "</div>";
}

// إعادة تعيين الجلسة
echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h4>🔄 إعادة تعيين الجلسة</h4>";
echo "<p>إعادة تعيين جلسة فتح القفل للاختبار</p>";
echo "<form method='POST'>";
echo "<button type='submit' name='reset_session' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>إعادة تعيين</button>";
echo "</form>";

if (isset($_POST['reset_session'])) {
    unset($_SESSION['site_unlocked']);
    echo "<div style='margin-top: 10px; padding: 10px; border-radius: 5px; background: #d4edda; color: #155724;'>";
    echo "✅ تم إعادة تعيين الجلسة";
    echo "</div>";
    echo "<script>setTimeout(function(){ location.reload(); }, 1000);</script>";
}
echo "</div>";

// محاكاة الحماية
echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h4>🎭 محاكاة الحماية</h4>";
echo "<p>اختبار كيف سيبدو الموقع للمستخدمين</p>";
echo "<form method='POST'>";
echo "<button type='submit' name='simulate_protection' style='background: #ffc107; color: #212529; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>محاكاة</button>";
echo "</form>";

if (isset($_POST['simulate_protection'])) {
    // محاكاة مؤقتة للحماية
    $_SESSION['temp_simulate'] = true;
    echo "<div style='margin-top: 10px; padding: 10px; border-radius: 5px; background: #fff3cd; color: #856404;'>";
    echo "⚠️ سيتم إعادة توجيهك لصفحة محاكاة الحماية...";
    echo "</div>";
    echo "<script>setTimeout(function(){ window.open('simulate_protection.php', '_blank'); }, 1000);</script>";
}
echo "</div>";

echo "</div>";

echo "<hr>";

// معلومات تقنية
echo "<h2>🔧 معلومات تقنية:</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>📊 معلومات قاعدة البيانات:</h4>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM site_protection");
    $count = $stmt->fetch()['count'];
    echo "<p><strong>عدد سجلات الحماية:</strong> $count</p>";
    
    $stmt = $pdo->query("SELECT * FROM site_protection ORDER BY id DESC LIMIT 1");
    $record = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($record) {
        echo "<p><strong>آخر تحديث:</strong> " . $record['updated_date'] . "</p>";
        echo "<p><strong>تاريخ الإنشاء:</strong> " . $record['created_date'] . "</p>";
    }
} catch(PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>🌐 معلومات الشبكة:</h4>";
echo "<p><strong>IP الحقيقي:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'غير محدد') . "</p>";
echo "<p><strong>IP المُمرر:</strong> " . ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'غير محدد') . "</p>";
echo "<p><strong>IP العميل:</strong> " . ($_SERVER['HTTP_CLIENT_IP'] ?? 'غير محدد') . "</p>";
echo "<p><strong>User Agent:</strong> " . htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد', 0, 100)) . "</p>";
echo "</div>";

echo "<hr>";

// الصفحات المحمية
echo "<h2>🛡️ الصفحات المحمية:</h2>";

$protected_pages = [
    'index.php' => 'الصفحة الرئيسية',
    'admin.php' => 'لوحة التحكم',
    'api_offers.php' => 'عروض API',
    'manage_offers.php' => 'إدارة العروض',
    'anti_duplicate_admin.php' => 'منع التكرار',
    'postback_admin.php' => 'Postback',
    'ip_check.php' => 'فحص IP'
];

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 20px 0;'>";

foreach ($protected_pages as $page => $title) {
    $status = $is_locked && !$is_ip_allowed && !isset($_SESSION['site_unlocked']) ? 'محمية' : 'متاحة';
    $color = $status === 'محمية' ? '#dc3545' : '#28a745';
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); border-left: 4px solid $color;'>";
    echo "<h5>$title</h5>";
    echo "<p style='color: $color; font-weight: 600;'>$status</p>";
    echo "<a href='$page' target='_blank' style='color: #007bff; text-decoration: none; font-size: 14px;'>اختبار الوصول</a>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='site_protection_admin.php' style='color: #007bff;'>🔑 إدارة كلمة مرور الموقع</a> - لتغيير إعدادات كلمة المرور</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - للوصول للإدارة العامة</p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - لاختبار تجربة المستخدم</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

form {
    margin: 10px 0;
}

input, button {
    font-family: inherit;
}
</style>
