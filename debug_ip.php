<?php
// ملف تشخيص مشكلة IP Quality Score
echo "<h1>🔍 تشخيص مشكلة IP Quality Score</h1>";

// الحصول على IP المستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';

echo "<h2>📋 معلومات أساسية:</h2>";
echo "<p><strong>IP المستخدم:</strong> $user_ip</p>";
echo "<p><strong>API Key:</strong> " . substr($api_key, 0, 10) . "..." . substr($api_key, -5) . "</p>";

echo "<hr>";

// اختبار 1: نفس الكود المستخدم في الصفحة الرئيسية (القديم)
echo "<h2>🧪 اختبار 1: الكود القديم (الصفحة الرئيسية)</h2>";
$api_url_old = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&fast=true";
echo "<p><strong>URL:</strong> $api_url_old</p>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response_old = @file_get_contents($api_url_old, false, $context);
if ($response_old !== false) {
    $data_old = json_decode($response_old, true);
    echo "<p style='color: green;'>✅ تم الحصول على استجابة</p>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
    echo json_encode($data_old, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    if (isset($data_old['fraud_score'])) {
        echo "<p><strong>🎯 Fraud Score:</strong> <span style='color: red; font-size: 1.2em;'>" . $data_old['fraud_score'] . "%</span></p>";
    } else {
        echo "<p style='color: red;'>❌ لا يوجد fraud_score في الاستجابة</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فشل في الحصول على استجابة</p>";
}

echo "<hr>";

// اختبار 2: نفس الكود المستخدم في صفحة فحص IP
echo "<h2>🧪 اختبار 2: الكود الجديد (صفحة فحص IP)</h2>";
$api_url_new = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";
echo "<p><strong>URL:</strong> $api_url_new</p>";

$response_new = @file_get_contents($api_url_new, false, $context);
if ($response_new !== false) {
    $data_new = json_decode($response_new, true);
    echo "<p style='color: green;'>✅ تم الحصول على استجابة</p>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
    echo json_encode($data_new, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    if (isset($data_new['fraud_score'])) {
        echo "<p><strong>🎯 Fraud Score:</strong> <span style='color: red; font-size: 1.2em;'>" . $data_new['fraud_score'] . "%</span></p>";
    } else {
        echo "<p style='color: red;'>❌ لا يوجد fraud_score في الاستجابة</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فشل في الحصول على استجابة</p>";
}

echo "<hr>";

// اختبار 3: بدون معاملات إضافية
echo "<h2>🧪 اختبار 3: بدون معاملات إضافية</h2>";
$api_url_simple = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip";
echo "<p><strong>URL:</strong> $api_url_simple</p>";

$response_simple = @file_get_contents($api_url_simple, false, $context);
if ($response_simple !== false) {
    $data_simple = json_decode($response_simple, true);
    echo "<p style='color: green;'>✅ تم الحصول على استجابة</p>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
    echo json_encode($data_simple, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    if (isset($data_simple['fraud_score'])) {
        echo "<p><strong>🎯 Fraud Score:</strong> <span style='color: red; font-size: 1.2em;'>" . $data_simple['fraud_score'] . "%</span></p>";
    } else {
        echo "<p style='color: red;'>❌ لا يوجد fraud_score في الاستجابة</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فشل في الحصول على استجابة</p>";
}

echo "<hr>";

// مقارنة النتائج
echo "<h2>📊 مقارنة النتائج:</h2>";
echo "<table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>الاختبار</th>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>الحالة</th>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>Fraud Score</th>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>Success</th>";
echo "</tr>";

// صف الاختبار الأول
echo "<tr>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>الكود القديم</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($response_old !== false ? '✅ نجح' : '❌ فشل') . "</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (isset($data_old['fraud_score']) ? $data_old['fraud_score'] . '%' : 'غير متوفر') . "</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (isset($data_old['success']) ? ($data_old['success'] ? 'true' : 'false') : 'غير متوفر') . "</td>";
echo "</tr>";

// صف الاختبار الثاني
echo "<tr>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>الكود الجديد</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($response_new !== false ? '✅ نجح' : '❌ فشل') . "</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (isset($data_new['fraud_score']) ? $data_new['fraud_score'] . '%' : 'غير متوفر') . "</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (isset($data_new['success']) ? ($data_new['success'] ? 'true' : 'false') : 'غير متوفر') . "</td>";
echo "</tr>";

// صف الاختبار الثالث
echo "<tr>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>بدون معاملات</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($response_simple !== false ? '✅ نجح' : '❌ فشل') . "</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (isset($data_simple['fraud_score']) ? $data_simple['fraud_score'] . '%' : 'غير متوفر') . "</td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (isset($data_simple['success']) ? ($data_simple['success'] ? 'true' : 'false') : 'غير متوفر') . "</td>";
echo "</tr>";

echo "</table>";

echo "<hr>";

// تحليل المشكلة
echo "<h2>🔍 تحليل المشكلة:</h2>";

if ($response_old !== false && $response_new !== false) {
    $score_old = $data_old['fraud_score'] ?? 'غير متوفر';
    $score_new = $data_new['fraud_score'] ?? 'غير متوفر';
    
    if ($score_old !== $score_new) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
        echo "<h3>⚠️ اختلاف في النتائج:</h3>";
        echo "<p>الكود القديم يعطي: <strong>$score_old</strong></p>";
        echo "<p>الكود الجديد يعطي: <strong>$score_new</strong></p>";
        echo "<p><strong>السبب المحتمل:</strong> المعاملات الإضافية تؤثر على حساب النتيجة</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h3>✅ النتائج متطابقة:</h3>";
        echo "<p>كلا الكودين يعطيان نفس النتيجة: <strong>$score_old</strong></p>";
        echo "</div>";
    }
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>";
    echo "<h3>❌ مشكلة في الاتصال:</h3>";
    echo "<p>تحقق من:</p>";
    echo "<ul>";
    echo "<li>صحة API Key</li>";
    echo "<li>الاتصال بالإنترنت</li>";
    echo "<li>حالة خدمة IP Quality Score</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a></p>";
echo "<p><a href='ip_check.php' style='color: #007bff;'>🔍 صفحة فحص IP</a></p>";
echo "<p><a href='index.php?debug=1' style='color: #007bff;'>🐛 الصفحة الرئيسية مع التشخيص</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #ddd;
    font-size: 12px;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
}
</style>
