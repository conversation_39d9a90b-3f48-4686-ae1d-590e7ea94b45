<?php
// ملف إصلاح مشاكل صفحة فحص IP
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح مشاكل صفحة فحص IP</h1>";

// فحص الملفات المطلوبة
echo "<h2>1️⃣ فحص الملفات:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

$files_to_check = [
    'ip_check.php' => 'صفحة فحص IP الرئيسية',
    'config.php' => 'ملف الإعدادات',
    'site_protection.php' => 'حماية الموقع',
    'api_usage.txt' => 'سجل استهلاك API (اختياري)'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description ($file)<br>";
        
        // فحص الصلاحيات
        if (is_readable($file)) {
            echo "  📖 قابل للقراءة<br>";
        } else {
            echo "  ❌ غير قابل للقراءة<br>";
        }
        
        if ($file === 'api_usage.txt' && file_exists($file)) {
            if (is_writable($file)) {
                echo "  ✏️ قابل للكتابة<br>";
            } else {
                echo "  ❌ غير قابل للكتابة<br>";
            }
        }
    } else {
        echo "❌ $description ($file) غير موجود<br>";
        
        if ($file === 'api_usage.txt') {
            echo "  ℹ️ سيتم إنشاؤه تلقائياً عند أول استخدام<br>";
        }
    }
}

echo "</div>";

// فحص إعدادات PHP
echo "<h2>2️⃣ فحص إعدادات PHP:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";

$php_settings = [
    'allow_url_fopen' => 'مطلوب لاستدعاء API',
    'file_get_contents' => 'دالة جلب المحتوى',
    'json_decode' => 'دالة فك تشفير JSON',
    'stream_context_create' => 'إنشاء سياق HTTP'
];

foreach ($php_settings as $setting => $description) {
    if ($setting === 'allow_url_fopen') {
        $status = ini_get($setting) ? '✅ مفعل' : '❌ معطل';
        echo "<strong>$description:</strong> $status<br>";
        
        if (!ini_get($setting)) {
            echo "  🔧 <strong>حل:</strong> فعل allow_url_fopen في php.ini<br>";
        }
    } else {
        $status = function_exists($setting) ? '✅ متوفرة' : '❌ غير متوفرة';
        echo "<strong>$description:</strong> $status<br>";
    }
}

echo "<br><strong>معلومات إضافية:</strong><br>";
echo "- PHP Version: " . PHP_VERSION . "<br>";
echo "- Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "- Max Execution Time: " . ini_get('max_execution_time') . " seconds<br>";
echo "- Default Socket Timeout: " . ini_get('default_socket_timeout') . " seconds<br>";

echo "</div>";

// اختبار API
echo "<h2>3️⃣ اختبار IP Quality Score API:</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";

$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$test_ip = '*******';

echo "<strong>🔑 API Key:</strong> " . substr($api_key, 0, 8) . "..." . substr($api_key, -4) . "<br>";
echo "<strong>🌐 Test IP:</strong> $test_ip<br><br>";

if (!ini_get('allow_url_fopen')) {
    echo "❌ <strong>خطأ:</strong> allow_url_fopen معطل. لا يمكن اختبار API.<br>";
} else {
    $api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$test_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";
    
    echo "<strong>📡 API URL:</strong><br>";
    echo "<code style='background: #f8f9fa; padding: 5px; border-radius: 3px; font-size: 11px; word-break: break-all;'>";
    echo htmlspecialchars($api_url);
    echo "</code><br><br>";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'method' => 'GET',
            'header' => [
                'Accept: application/json',
                'Connection: close'
            ]
        ]
    ]);
    
    echo "<strong>🔗 اختبار الاتصال...</strong><br>";
    $start_time = microtime(true);
    $response = @file_get_contents($api_url, false, $context);
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    if ($response !== false) {
        echo "✅ تم الاتصال بنجاح<br>";
        echo "⏱️ وقت الاستجابة: {$response_time}ms<br>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ تم فك تشفير JSON بنجاح<br>";
            
            if (isset($data['success']) && $data['success']) {
                echo "✅ API يعمل بشكل صحيح<br>";
                echo "<strong>📊 نتائج الاختبار:</strong><br>";
                echo "- البلد: " . ($data['country_code'] ?? 'غير محدد') . "<br>";
                echo "- المدينة: " . ($data['city'] ?? 'غير محدد') . "<br>";
                echo "- مزود الخدمة: " . ($data['ISP'] ?? 'غير محدد') . "<br>";
                echo "- نقاط الاحتيال: " . ($data['fraud_score'] ?? 0) . "/100<br>";
                echo "- بروكسي: " . (($data['proxy'] ?? false) ? 'نعم' : 'لا') . "<br>";
                echo "- Tor: " . (($data['tor'] ?? false) ? 'نعم' : 'لا') . "<br>";
            } else {
                echo "❌ API أرجع خطأ<br>";
                echo "<strong>رسالة الخطأ:</strong> " . ($data['message'] ?? 'غير محدد') . "<br>";
                
                if (isset($data['errors'])) {
                    echo "<strong>تفاصيل الأخطاء:</strong><br>";
                    foreach ($data['errors'] as $error) {
                        echo "- " . htmlspecialchars($error) . "<br>";
                    }
                }
            }
        } else {
            echo "❌ فشل في فك تشفير JSON<br>";
            echo "<strong>الاستجابة الخام (أول 500 حرف):</strong><br>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars(substr($response, 0, 500));
            echo "</pre>";
        }
    } else {
        echo "❌ فشل في الاتصال<br>";
        
        $error = error_get_last();
        if ($error) {
            echo "<strong>تفاصيل الخطأ:</strong><br>";
            echo "- النوع: " . $error['type'] . "<br>";
            echo "- الرسالة: " . htmlspecialchars($error['message']) . "<br>";
            echo "- الملف: " . $error['file'] . ":" . $error['line'] . "<br>";
        }
        
        echo "<br><strong>🔧 حلول محتملة:</strong><br>";
        echo "1. تحقق من الاتصال بالإنترنت<br>";
        echo "2. تأكد من صحة API Key<br>";
        echo "3. فحص إعدادات الجدار الناري<br>";
        echo "4. جرب زيادة timeout<br>";
    }
}

echo "</div>";

// فحص ملف السجل
echo "<h2>4️⃣ فحص ملف سجل API:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";

$log_file = 'api_usage.txt';

if (file_exists($log_file)) {
    echo "✅ ملف السجل موجود<br>";
    
    $file_size = filesize($log_file);
    echo "📁 حجم الملف: " . number_format($file_size) . " بايت<br>";
    
    if (is_readable($log_file)) {
        echo "✅ قابل للقراءة<br>";
        
        $logs = @file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        if ($logs !== false) {
            $total_logs = count($logs);
            echo "📊 عدد السجلات: $total_logs<br>";
            
            if ($total_logs > 0) {
                echo "<strong>📋 آخر 5 سجلات:</strong><br>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
                $recent_logs = array_slice($logs, -5);
                foreach ($recent_logs as $log) {
                    echo htmlspecialchars($log) . "\n";
                }
                echo "</pre>";
            }
        } else {
            echo "❌ فشل في قراءة الملف<br>";
        }
    } else {
        echo "❌ غير قابل للقراءة<br>";
    }
    
    if (is_writable($log_file)) {
        echo "✅ قابل للكتابة<br>";
    } else {
        echo "❌ غير قابل للكتابة<br>";
        echo "🔧 <strong>حل:</strong> تغيير صلاحيات الملف<br>";
    }
} else {
    echo "⚠️ ملف السجل غير موجود<br>";
    echo "ℹ️ سيتم إنشاؤه تلقائياً عند أول استخدام<br>";
    
    // اختبار إنشاء الملف
    $test_content = date('Y-m-d H:i:s') . " - Test entry\n";
    if (@file_put_contents($log_file, $test_content, FILE_APPEND | LOCK_EX)) {
        echo "✅ تم إنشاء ملف السجل بنجاح<br>";
        
        // حذف الملف التجريبي
        @unlink($log_file);
    } else {
        echo "❌ فشل في إنشاء ملف السجل<br>";
        echo "🔧 <strong>حل:</strong> تحقق من صلاحيات المجلد<br>";
    }
}

// فحص صلاحيات المجلد
if (is_writable('.')) {
    echo "✅ المجلد الحالي قابل للكتابة<br>";
} else {
    echo "❌ المجلد الحالي غير قابل للكتابة<br>";
    echo "🔧 <strong>حل:</strong> تغيير صلاحيات المجلد<br>";
}

echo "</div>";

// إجراءات الإصلاح
echo "<h2>5️⃣ إجراءات الإصلاح التلقائي:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";

$fixes_applied = 0;

// إنشاء ملف السجل إذا لم يكن موجوداً
if (!file_exists('api_usage.txt')) {
    if (@touch('api_usage.txt')) {
        echo "✅ تم إنشاء ملف api_usage.txt<br>";
        $fixes_applied++;
    } else {
        echo "❌ فشل في إنشاء ملف api_usage.txt<br>";
    }
}

// فحص وإصلاح صلاحيات الملف
if (file_exists('api_usage.txt') && !is_writable('api_usage.txt')) {
    if (@chmod('api_usage.txt', 0666)) {
        echo "✅ تم إصلاح صلاحيات ملف api_usage.txt<br>";
        $fixes_applied++;
    } else {
        echo "❌ فشل في إصلاح صلاحيات ملف api_usage.txt<br>";
    }
}

if ($fixes_applied === 0) {
    echo "ℹ️ لا توجد مشاكل تحتاج إصلاح تلقائي<br>";
} else {
    echo "🎉 تم تطبيق $fixes_applied إصلاح<br>";
}

echo "</div>";

// دليل حل المشاكل
echo "<h2>6️⃣ دليل حل المشاكل الشائعة:</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";

echo "<strong>🚨 المشاكل الشائعة وحلولها:</strong><br><br>";

echo "<strong>1. رسالة 'allow_url_fopen معطل':</strong><br>";
echo "- فعل allow_url_fopen في ملف php.ini<br>";
echo "- أعد تشغيل الخادم<br>";
echo "- تواصل مع مزود الاستضافة<br><br>";

echo "<strong>2. 'فشل في الاتصال بخدمة فحص IP':</strong><br>";
echo "- تحقق من الاتصال بالإنترنت<br>";
echo "- فحص إعدادات الجدار الناري<br>";
echo "- تأكد من صحة API Key<br>";
echo "- جرب IP مختلف للاختبار<br><br>";

echo "<strong>3. 'تم الوصول للحد الأقصى الشهري':</strong><br>";
echo "- انتظر حتى الشهر القادم<br>";
echo "- احذف ملف api_usage.txt لإعادة تعيين العداد<br>";
echo "- راقب الاستهلاك بانتظام<br><br>";

echo "<strong>4. 'فشل في تسجيل استهلاك API':</strong><br>";
echo "- تحقق من صلاحيات الكتابة<br>";
echo "- تأكد من وجود مساحة كافية<br>";
echo "- فحص صلاحيات المجلد<br><br>";

echo "<strong>5. 'تنسيق عنوان IP غير صحيح':</strong><br>";
echo "- استخدم تنسيق IPv4 صحيح (مثل: ***********)<br>";
echo "- تجنب المسافات والرموز الإضافية<br>";
echo "- جرب IP معروف مثل *******<br>";

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='ip_check.php' style='color: #007bff;'>🔍 صفحة فحص IP</a> - الصفحة الأصلية</p>";
echo "<p><a href='test_ip_check.php' style='color: #007bff;'>🧪 اختبار شامل</a> - فحص تفصيلي</p>";
echo "<p><a href='api_usage_details.php' style='color: #007bff;'>📊 تفاصيل الاستهلاك</a> - سجل API</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - الإدارة العامة</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 13px;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
}

strong {
    color: #333;
}
</style>
