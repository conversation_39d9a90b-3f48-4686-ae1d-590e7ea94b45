/* 
 * Country Tooltips CSS
 * نظام tooltips لعرض أسماء البلدان كاملة عند الوقوف على الأسماء المختصرة
 * يمكن استخدامه في جميع صفحات الموقع
 */

/* الأنماط الأساسية للـ tooltips */
.country-tooltip, 
.country-more-tooltip,
.country-item, 
.country-more {
    cursor: help;
    position: relative;
    border-bottom: 1px dotted currentColor;
    transition: all 0.3s ease;
    display: inline;
}

/* تأثير الـ hover */
.country-tooltip:hover, 
.country-more-tooltip:hover,
.country-item:hover, 
.country-more:hover {
    border-bottom-style: solid;
    text-shadow: 0 0 3px currentColor;
}

/* الـ tooltip نفسه */
.country-tooltip[title]:hover::after,
.country-more-tooltip[title]:hover::after,
.country-item[title]:hover::after,
.country-more[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.95);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    animation: tooltipFadeIn 0.3s ease;
    margin-bottom: 5px;
    max-width: 250px;
    white-space: normal;
    text-align: center;
    line-height: 1.4;
    font-weight: normal;
    text-shadow: none;
}

/* السهم الصغير للـ tooltip */
.country-tooltip[title]:hover::before,
.country-more-tooltip[title]:hover::before,
.country-item[title]:hover::before,
.country-more[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(1px);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.95);
    z-index: 1001;
}

/* أنيميشن ظهور الـ tooltip */
@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* تخصيصات للصفحة الرئيسية */
.country-badge .country-item,
.country-badge .country-more {
    border-bottom-color: rgba(255, 255, 255, 0.7);
}

.country-badge .country-item:hover,
.country-badge .country-more:hover {
    border-bottom-color: white;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

/* تخصيصات لصفحات الإدارة */
.offer-info .country-tooltip,
.offer-info .country-more-tooltip {
    border-bottom-color: #007bff;
}

.offer-info .country-tooltip:hover,
.offer-info .country-more-tooltip:hover {
    border-bottom-color: #0056b3;
    color: #0056b3;
}

/* تخصيصات لصفحة عروض API */
.offer-details .country-tooltip,
.offer-details .country-more-tooltip {
    border-bottom-color: #007bff;
}

.offer-details .country-tooltip:hover,
.offer-details .country-more-tooltip:hover {
    border-bottom-color: #0056b3;
    color: #0056b3;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .country-tooltip[title]:hover::after,
    .country-more-tooltip[title]:hover::after,
    .country-item[title]:hover::after,
    .country-more[title]:hover::after {
        font-size: 11px;
        padding: 6px 10px;
        max-width: 200px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .country-tooltip[title]:hover::after,
    .country-more-tooltip[title]:hover::after,
    .country-item[title]:hover::after,
    .country-more[title]:hover::after {
        font-size: 13px;
        padding: 10px 14px;
        max-width: 300px;
    }
}

/* تأثيرات خاصة للبلدان العامة */
.country-global {
    background: linear-gradient(45deg, #28a745, #20c997);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* تأثيرات للبلدان المميزة */
.country-featured {
    background: linear-gradient(45deg, #007bff, #6610f2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* تأثيرات للبلدان العربية */
.country-arabic {
    background: linear-gradient(45deg, #fd7e14, #e83e8c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* تحسينات الأداء */
.country-tooltip,
.country-more-tooltip,
.country-item,
.country-more {
    will-change: transform, opacity;
}

/* إخفاء الـ tooltips على الأجهزة اللمسية */
@media (hover: none) {
    .country-tooltip[title]:hover::after,
    .country-more-tooltip[title]:hover::after,
    .country-item[title]:hover::after,
    .country-more[title]:hover::after,
    .country-tooltip[title]:hover::before,
    .country-more-tooltip[title]:hover::before,
    .country-item[title]:hover::before,
    .country-more[title]:hover::before {
        display: none;
    }
    
    /* بدلاً من ذلك، إظهار النص كاملاً على الأجهزة اللمسية */
    .country-tooltip,
    .country-more-tooltip,
    .country-item,
    .country-more {
        border-bottom: none;
        cursor: default;
    }
}

/* تأثيرات إضافية للتفاعل */
.country-tooltip:active,
.country-more-tooltip:active,
.country-item:active,
.country-more:active {
    transform: scale(0.98);
}

/* تحسينات الوصولية */
.country-tooltip:focus,
.country-more-tooltip:focus,
.country-item:focus,
.country-more:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    border-radius: 2px;
}

/* تأثيرات للطباعة */
@media print {
    .country-tooltip[title]:after {
        content: " (" attr(title) ")";
        font-size: 0.8em;
        color: #666;
    }
    
    .country-tooltip,
    .country-more-tooltip,
    .country-item,
    .country-more {
        border-bottom: none;
    }
}
