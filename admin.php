<?php
session_start();
require_once 'config.php';

// معالجة النموذج
$message = '';

// جلب العروض من قاعدة البيانات
$offers = getAllOffers($pdo);

// جلب الإحصائيات
$usersCount = countUsers($pdo);
$offersCount = countOffers($pdo);
$conversionsCount = countConversions($pdo);
$totalEarnings = getTotalEarnings($pdo);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_offer'])) {
        $offerName = cleanInput($_POST['offer_name']);
        $offerLink = cleanInput($_POST['offer_link']);
        $offerImage = cleanInput($_POST['offer_image']);
        $offerDescription = cleanInput($_POST['offer_description']);
        $offerTerms = cleanInput($_POST['offer_terms']);

        if (!empty($offerName) && !empty($offerLink) && !empty($offerImage)) {
            // التحقق من صحة الروابط
            if (filter_var($offerLink, FILTER_VALIDATE_URL) && filter_var($offerImage, FILTER_VALIDATE_URL)) {
                if (insertOffer($pdo, $offerName, $offerLink, $offerImage, $offerDescription, $offerTerms)) {
                    $message = '<div class="success">تم إضافة العرض بنجاح! ✅</div>';
                    // إعادة جلب العروض لعرض التحديث
                    $offers = getAllOffers($pdo);
                } else {
                    $message = '<div class="error">حدث خطأ أثناء إضافة العرض. يرجى المحاولة مرة أخرى!</div>';
                }
            } else {
                $message = '<div class="error">يرجى إدخال روابط صحيحة!</div>';
            }
        } else {
            $message = '<div class="error">يرجى ملء الحقول المطلوبة!</div>';
        }
    } elseif (isset($_POST['delete_offer'])) {
        $offerId = (int)$_POST['offer_id'];
        if (deleteOffer($pdo, $offerId)) {
            $message = '<div class="success">تم حذف العرض بنجاح! 🗑️</div>';
            // إعادة جلب العروض لعرض التحديث
            $offers = getAllOffers($pdo);
        } else {
            $message = '<div class="error">حدث خطأ أثناء حذف العرض!</div>';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - إدارة العروض</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"],
        input[type="url"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus,
        input[type="url"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .offer-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .offer-card:hover {
            transform: translateY(-5px);
        }
        
        .offer-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .offer-content {
            padding: 20px;
        }
        
        .offer-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .offer-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            word-break: break-all;
            margin-bottom: 15px;
            display: block;
        }
        
        .offer-date {
            color: #999;
            font-size: 12px;
            margin-bottom: 15px;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .delete-btn:hover {
            background: #c82333;
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1em;
            font-weight: 600;
        }

        .stat-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 لوحة التحكم</h1>
            <p>إدارة العروض والمحتوى</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
                <a href="postback_admin.php" class="nav-link">📊 Postback</a>
                <a href="ip_check.php" class="nav-link">🔍 فحص الـ IP</a>
            </div>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number"><?php echo $usersCount; ?></div>
                <div class="stat-label">المستخدمون المسجلون</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-number"><?php echo $offersCount; ?></div>
                <div class="stat-label">العروض المتاحة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number"><?php echo date('Y-m-d'); ?></div>
                <div class="stat-label">تاريخ اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔍</div>
                <div class="stat-number">IP</div>
                <div class="stat-label">فحص جودة الـ IP</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🌐</div>
                <div class="stat-number">API</div>
                <div class="stat-label">عروض من API</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔄</div>
                <div class="stat-number"><?php echo $conversionsCount; ?></div>
                <div class="stat-label">التحويلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-number">$<?php echo number_format($totalEarnings, 2); ?></div>
                <div class="stat-label">إجمالي الأرباح</div>
            </div>
        </div>

        <div class="form-section">
            <h2>➕ إضافة عرض جديد</h2>
            
            <?php echo $message; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="offer_name">اسم العرض:</label>
                    <input type="text" 
                           id="offer_name" 
                           name="offer_name" 
                           placeholder="مثال: عرض خاص على المنتجات"
                           required>
                </div>
                
                <div class="form-group">
                    <label for="offer_link">رابط العرض:</label>
                    <input type="url" 
                           id="offer_link" 
                           name="offer_link" 
                           placeholder="https://example.com/offer"
                           required>
                </div>
                
                <div class="form-group">
                    <label for="offer_image">رابط صورة العرض:</label>
                    <input type="url"
                           id="offer_image"
                           name="offer_image"
                           placeholder="https://example.com/image.jpg"
                           required>
                </div>

                <div class="form-group">
                    <label for="offer_description">وصف العرض (اختياري):</label>
                    <textarea id="offer_description"
                              name="offer_description"
                              rows="3"
                              placeholder="وصف مفصل للعرض..."
                              style="width: 100%; padding: 12px 15px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <label for="offer_terms">الشروط الإضافية (اختياري):</label>
                    <textarea id="offer_terms"
                              name="offer_terms"
                              rows="3"
                              placeholder="شروط وأحكام إضافية للعرض..."
                              style="width: 100%; padding: 12px 15px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; resize: vertical;"></textarea>
                </div>

                <button type="submit" name="add_offer">🎯 إضافة العرض</button>
            </form>
        </div>
        
        <?php if (!empty($offers)): ?>
        <div class="form-section">
            <h2>📋 العروض الحالية (<?php echo count($offers); ?>)</h2>
            
            <div class="offers-grid">
                <?php foreach ($offers as $offer): ?>
                <div class="offer-card">
                    <img src="<?php echo htmlspecialchars($offer['image']); ?>"
                         alt="<?php echo htmlspecialchars($offer['name']); ?>"
                         class="offer-image"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'">

                    <div class="offer-content">
                        <div class="offer-name"><?php echo htmlspecialchars($offer['name']); ?></div>

                        <?php if (!empty($offer['description'])): ?>
                        <div style="color: #666; font-size: 14px; margin: 10px 0; line-height: 1.4;">
                            <strong>الوصف:</strong> <?php echo htmlspecialchars(substr($offer['description'], 0, 150)); ?><?php echo strlen($offer['description']) > 150 ? '...' : ''; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($offer['additional_terms'])): ?>
                        <div style="color: #666; font-size: 14px; margin: 10px 0; line-height: 1.4;">
                            <strong>الشروط الإضافية:</strong> <?php echo htmlspecialchars(substr($offer['additional_terms'], 0, 100)); ?><?php echo strlen($offer['additional_terms']) > 100 ? '...' : ''; ?>
                        </div>
                        <?php endif; ?>

                        <a href="<?php echo htmlspecialchars($offer['link']); ?>"
                           target="_blank"
                           class="offer-link">
                            🔗 <?php echo htmlspecialchars($offer['link']); ?>
                        </a>
                        <div class="offer-date">تاريخ الإضافة: <?php echo date('Y-m-d H:i:s', strtotime($offer['created_date'])); ?></div>

                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                            <button type="submit"
                                    name="delete_offer"
                                    class="delete-btn"
                                    onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                🗑️ حذف
                            </button>
                        </form>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
