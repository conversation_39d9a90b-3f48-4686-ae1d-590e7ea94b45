<?php
session_start();
require_once 'config.php';
require_once 'site_protection.php';

// معالجة النموذج
$message = '';

// حفظ إعدادات Postback
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $postback_url = cleanInput($_POST['postback_url']);
    $postback_password = cleanInput($_POST['postback_password']);
    $is_enabled = isset($_POST['is_enabled']) ? 1 : 0;
    
    if (savePostbackSettings($pdo, $postback_url, $postback_password, $is_enabled)) {
        $message = '<div class="success">تم حفظ إعدادات Postback بنجاح! ✅</div>';
    } else {
        $message = '<div class="error">فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.</div>';
    }
}

// جلب الإعدادات الحالية
$settings = getPostbackSettings($pdo);
$conversions = getAllConversions($pdo);
$total_conversions = countConversions($pdo);
$total_earnings = getTotalEarnings($pdo);

// إنشاء رابط Postback الكامل
$postback_url = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/postback.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة Postback والتحويلات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .stat-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"], input[type="url"], input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="url"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        input[type="checkbox"] {
            transform: scale(1.2);
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .conversions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .conversions-table th,
        .conversions-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .conversions-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .conversions-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .home-link {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            font-size: 16px;
        }

        .home-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 إدارة Postback والتحويلات</h1>
            <p>تتبع وإدارة تحويلات CPAlead</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
                <a href="manage_offers.php" class="nav-link">🗂️ إدارة العروض</a>
                <a href="anti_duplicate_admin.php" class="nav-link">🛡️ منع التكرار</a>
                <a href="postback_admin.php" class="nav-link">📊 Postback</a>
                <a href="ip_check.php" class="nav-link">🔍 فحص الـ IP</a>
            </div>
        </div>
        
        <!-- إحصائيات التحويلات -->
        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-icon">🔄</div>
                <div class="stat-number"><?php echo $total_conversions; ?></div>
                <div class="stat-label">إجمالي التحويلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-number">$<?php echo number_format($total_earnings, 2); ?></div>
                <div class="stat-label">إجمالي الأرباح</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-number"><?php echo $settings && $settings['is_enabled'] ? 'مفعل' : 'معطل'; ?></div>
                <div class="stat-label">حالة Postback</div>
            </div>
        </div>
        
        <!-- جدول التحويلات -->
        <?php if (!empty($conversions)): ?>
        <div class="form-section">
            <h2>📋 سجل التحويلات</h2>

            <table class="conversions-table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>Campaign ID</th>
                        <th>SubID</th>
                        <th>المبلغ</th>
                        <th>الدولة</th>
                        <th>IP</th>
                        <th>Lead ID</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (array_slice($conversions, 0, 50) as $conversion): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i', strtotime($conversion['conversion_date'])); ?></td>
                        <td><?php echo htmlspecialchars($conversion['campaign_id']); ?></td>
                        <td><?php echo htmlspecialchars($conversion['subid']); ?></td>
                        <td>$<?php echo number_format($conversion['payout'], 2); ?></td>
                        <td><?php echo htmlspecialchars($conversion['country_iso']); ?></td>
                        <td><?php echo htmlspecialchars($conversion['ip_address']); ?></td>
                        <td><?php echo htmlspecialchars($conversion['lead_id']); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $conversion['status']; ?>">
                                <?php
                                switch($conversion['status']) {
                                    case 'pending': echo 'معلق'; break;
                                    case 'confirmed': echo 'مؤكد'; break;
                                    case 'rejected': echo 'مرفوض'; break;
                                    default: echo $conversion['status'];
                                }
                                ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if (count($conversions) > 50): ?>
            <p style="text-align: center; margin-top: 20px; color: #666;">
                عرض أول 50 تحويل من إجمالي <?php echo count($conversions); ?> تحويل
            </p>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- إعدادات Postback -->
        <div class="form-section">
            <h2>⚙️ إعدادات Postback</h2>
            
            <?php echo $message; ?>
            
            <div class="info-box">
                <h3>📋 معلومات مهمة:</h3>
                <p><strong>رابط Postback الخاص بك:</strong></p>
                <div class="code-block"><?php echo htmlspecialchars($postback_url); ?></div>
                <p><strong>IP المسموح:</strong> ************ (CPAlead Server)</p>
                <p><strong>طريقة الإرسال:</strong> HTTP GET Request</p>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="postback_url">رابط Postback الخارجي (اختياري):</label>
                    <input type="url" 
                           id="postback_url" 
                           name="postback_url" 
                           value="<?php echo htmlspecialchars($settings['postback_url'] ?? ''); ?>"
                           placeholder="https://example.com/postback?subid={subid}&payout={payout}">
                    <small>يمكنك إرسال التحويلات لخادم آخر أيضاً</small>
                </div>
                
                <div class="form-group">
                    <label for="postback_password">كلمة مرور Postback (اختياري):</label>
                    <input type="password" 
                           id="postback_password" 
                           name="postback_password" 
                           value="<?php echo htmlspecialchars($settings['postback_password'] ?? ''); ?>"
                           placeholder="كلمة مرور للحماية الإضافية">
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" 
                               id="is_enabled" 
                               name="is_enabled" 
                               <?php echo ($settings && $settings['is_enabled']) ? 'checked' : ''; ?>>
                        <label for="is_enabled">تفعيل استقبال Postback</label>
                    </div>
                </div>
                
                <button type="submit" name="save_settings">💾 حفظ الإعدادات</button>
            </form>
            
            <div class="info-box" style="margin-top: 30px;">
                <h3>🔧 إعداد CPAlead Dashboard:</h3>
                <p>في لوحة تحكم CPAlead، اذهب إلى Postback → Configuration وأدخل:</p>
                <div class="code-block"><?php echo htmlspecialchars($postback_url); ?>?campaign_id={campaign_id}&subid={subid}&payout={payout}&ip_address={ip_address}&lead_id={lead_id}&country_iso={country_iso}<?php echo !empty($settings['postback_password']) ? '&password=' . htmlspecialchars($settings['postback_password']) : ''; ?></div>
            </div>
        </div>

    </div>

    <!-- زر عائم للصفحة الرئيسية -->
    <a href="index.php" class="home-link">🏠 الرئيسية</a>
</body>
</html>
