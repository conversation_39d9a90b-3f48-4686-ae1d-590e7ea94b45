<?php
// ملف اختبار سريع للدوال بعد الإصلاح
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 اختبار الدوال بعد الإصلاح</h1>";

// تضمين الملفات الأساسية
echo "<h2>1️⃣ تضمين الملفات:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

try {
    require_once 'config.php';
    echo "✅ تم تضمين config.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تضمين config.php: " . htmlspecialchars($e->getMessage()) . "<br>";
    exit;
}

echo "</div>";

// اختبار الدوال الأساسية
echo "<h2>2️⃣ اختبار الدوال الأساسية:</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";

$functions_to_test = [
    'getUserIP' => 'الحصول على IP المستخدم',
    'getCountryName' => 'الحصول على اسم البلد',
    'recordClick' => 'تسجيل النقرة',
    'isIPBlocked' => 'فحص حجب IP',
    'getClickStats' => 'إحصائيات النقرات',
    'insertOffer' => 'إدراج عرض',
    'getAllOffers' => 'جلب جميع العروض',
    'getOffersByCountry' => 'جلب العروض حسب البلد'
];

foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        echo "✅ <strong>$function</strong> - $description<br>";
    } else {
        echo "❌ <strong>$function</strong> - $description (غير موجودة)<br>";
    }
}

echo "</div>";

// اختبار تشغيل الدوال
echo "<h2>3️⃣ اختبار تشغيل الدوال:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";

// اختبار getUserIP
try {
    if (function_exists('getUserIP')) {
        $user_ip = getUserIP();
        echo "✅ <strong>getUserIP():</strong> $user_ip<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>getUserIP():</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
}

// اختبار getCountryName
try {
    if (function_exists('getCountryName')) {
        $country_us = getCountryName('US');
        $country_eg = getCountryName('EG');
        echo "✅ <strong>getCountryName('US'):</strong> $country_us<br>";
        echo "✅ <strong>getCountryName('EG'):</strong> $country_eg<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>getCountryName():</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
}

// اختبار قاعدة البيانات
try {
    if (isset($pdo) && $pdo instanceof PDO) {
        echo "✅ <strong>قاعدة البيانات:</strong> متصلة<br>";
        
        // اختبار getAllOffers
        if (function_exists('getAllOffers')) {
            $offers = getAllOffers($pdo);
            echo "✅ <strong>getAllOffers():</strong> " . count($offers) . " عرض<br>";
        }
        
        // اختبار getOffersByCountry
        if (function_exists('getOffersByCountry')) {
            $offers_us = getOffersByCountry($pdo, 'US');
            echo "✅ <strong>getOffersByCountry('US'):</strong> " . count($offers_us) . " عرض<br>";
        }
        
        // اختبار getClickStats
        if (function_exists('getClickStats')) {
            $stats = getClickStats($pdo);
            echo "✅ <strong>getClickStats():</strong> " . $stats['total_clicks'] . " نقرة إجمالية<br>";
        }
        
    } else {
        echo "❌ <strong>قاعدة البيانات:</strong> غير متصلة<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>قاعدة البيانات:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "</div>";

// اختبار الصفحات الرئيسية
echo "<h2>4️⃣ اختبار الصفحات:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";

$pages_to_test = [
    'index.php' => 'الصفحة الرئيسية',
    'ip_check.php' => 'فحص IP',
    'admin.php' => 'لوحة التحكم',
    'manage_offers.php' => 'إدارة العروض',
    'api_offers.php' => 'عروض API'
];

foreach ($pages_to_test as $page => $description) {
    if (file_exists($page)) {
        // فحص syntax
        $output = [];
        $return_var = 0;
        exec("php -l \"$page\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "✅ <strong>$page</strong> - $description (syntax صحيح)<br>";
        } else {
            echo "❌ <strong>$page</strong> - $description (أخطاء syntax)<br>";
            echo "<pre style='background: #f8f9fa; padding: 5px; border-radius: 3px; font-size: 11px; margin: 5px 0;'>";
            echo htmlspecialchars(implode("\n", array_slice($output, 0, 3)));
            echo "</pre>";
        }
    } else {
        echo "⚠️ <strong>$page</strong> - $description (غير موجود)<br>";
    }
}

echo "</div>";

// اختبار محاكاة استخدام
echo "<h2>5️⃣ اختبار محاكاة الاستخدام:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";

try {
    // محاكاة تسجيل نقرة
    if (function_exists('recordClick') && isset($pdo)) {
        $test_ip = getUserIP();
        $test_user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Test Agent';
        
        echo "<strong>🔄 محاكاة تسجيل نقرة:</strong><br>";
        echo "- IP: $test_ip<br>";
        echo "- User Agent: " . htmlspecialchars(substr($test_user_agent, 0, 50)) . "...<br>";
        
        // لا نسجل نقرة حقيقية، فقط نختبر الدالة
        echo "✅ دالة recordClick جاهزة للاستخدام<br>";
    }
    
    // محاكاة فحص حجب IP
    if (function_exists('isIPBlocked') && isset($pdo)) {
        $test_ip = getUserIP();
        $is_blocked = isIPBlocked($pdo, 1, $test_ip); // افتراض وجود عرض بـ ID 1
        
        echo "<strong>🛡️ فحص حجب IP:</strong><br>";
        echo "- IP: $test_ip<br>";
        echo "- الحالة: " . ($is_blocked ? 'محجوب' : 'غير محجوب') . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>خطأ في المحاكاة:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "</div>";

// نتيجة الاختبار
echo "<h2>6️⃣ نتيجة الاختبار:</h2>";

$total_functions = count($functions_to_test);
$working_functions = 0;

foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        $working_functions++;
    }
}

$success_rate = round(($working_functions / $total_functions) * 100, 1);

if ($success_rate >= 90) {
    $status_color = '#d4edda';
    $text_color = '#155724';
    $status_icon = '🎉';
    $status_text = 'ممتاز';
} elseif ($success_rate >= 70) {
    $status_color = '#fff3cd';
    $text_color = '#856404';
    $status_icon = '⚠️';
    $status_text = 'جيد مع تحذيرات';
} else {
    $status_color = '#f8d7da';
    $text_color = '#721c24';
    $status_icon = '❌';
    $status_text = 'يحتاج إصلاح';
}

echo "<div style='background: $status_color; color: $text_color; padding: 20px; border-radius: 8px; text-align: center;'>";
echo "<h3>$status_icon $status_text</h3>";
echo "<p><strong>معدل النجاح:</strong> $success_rate% ($working_functions من $total_functions دالة تعمل)</p>";

if ($success_rate >= 90) {
    echo "<p>✅ جميع الدوال تعمل بشكل صحيح. يمكنك استخدام الموقع بأمان.</p>";
} elseif ($success_rate >= 70) {
    echo "<p>⚠️ معظم الدوال تعمل، لكن هناك بعض المشاكل البسيطة.</p>";
} else {
    echo "<p>❌ هناك مشاكل كبيرة تحتاج إصلاح فوري.</p>";
}

echo "</div>";

echo "<hr>";
echo "<h2>🔗 الخطوات التالية:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

if ($success_rate >= 90) {
    echo "<strong>🎯 يمكنك الآن:</strong><br>";
    echo "1. <a href='index.php' style='color: #007bff;'>🏠 زيارة الصفحة الرئيسية</a><br>";
    echo "2. <a href='ip_check.php' style='color: #007bff;'>🔍 استخدام فحص IP</a><br>";
    echo "3. <a href='admin.php' style='color: #007bff;'>⚙️ دخول لوحة التحكم</a><br>";
    echo "4. <a href='manage_offers.php' style='color: #007bff;'>🗂️ إدارة العروض</a><br>";
} else {
    echo "<strong>🔧 يجب عليك:</strong><br>";
    echo "1. <a href='fix_duplicate_functions.php' style='color: #dc3545;'>🔧 تشغيل إصلاح الدوال المكررة</a><br>";
    echo "2. فحص ملف config.php للتأكد من وجود جميع الدوال<br>";
    echo "3. التأكد من اتصال قاعدة البيانات<br>";
    echo "4. إعادة تشغيل هذا الاختبار<br>";
}

echo "</div>";

// معلومات إضافية
echo "<hr>";
echo "<h2>ℹ️ معلومات الجلسة:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; font-size: 14px;'>";
echo "<strong>وقت الاختبار:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Memory Usage:</strong> " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB<br>";
echo "<strong>Execution Time:</strong> " . number_format(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 3) . " seconds<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
    margin: 5px 0;
}

strong {
    color: #333;
}
</style>
