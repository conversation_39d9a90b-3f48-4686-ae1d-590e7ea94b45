<?php
// ملف اختبار تحويل العروض من CPAlead API
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🔄 اختبار تحويل العروض من CPAlead API</h1>";

// محاكاة بيانات من CPAlead API
$sample_api_data = [
    [
        'id' => 12345,
        'title' => 'عرض تجريبي 1',
        'link' => 'https://example.com/offer1',
        'amount' => 2.50,
        'payout_type' => 'CPA',
        'device' => 'mobile',
        'countries' => ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES'],
        'conversion' => 'Complete survey and email submit',
        'description' => 'هذا عرض تجريبي للاختبار',
        'creatives' => [
            'url' => 'https://via.placeholder.com/300x200?text=Offer+1'
        ]
    ],
    [
        'id' => 12346,
        'title' => 'عرض تجريبي 2',
        'link' => 'https://example.com/offer2',
        'amount' => 1.75,
        'payout_type' => 'CPI',
        'device' => 'desktop',
        'geo' => ['EG', 'SA', 'AE', 'KW', 'QA'],
        'conversion' => 'Install mobile app',
        'description' => 'عرض آخر للاختبار',
        'creatives' => [
            'url' => 'https://via.placeholder.com/300x200?text=Offer+2'
        ]
    ],
    [
        'id' => 12347,
        'title' => 'عرض عالمي',
        'link' => 'https://example.com/offer3',
        'amount' => 5.00,
        'payout_type' => 'CPA',
        'device' => 'all',
        'countries' => [], // فارغ = جميع البلدان
        'conversion' => 'Sign up and verify email',
        'description' => 'عرض متاح لجميع البلدان',
        'creatives' => [
            'url' => 'https://via.placeholder.com/300x200?text=Global+Offer'
        ]
    ]
];

echo "<h2>1️⃣ بيانات العروض الأصلية من API:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<pre>" . htmlspecialchars(json_encode($sample_api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
echo "</div>";

echo "<h2>2️⃣ معالجة البيانات وتحويلها:</h2>";

foreach ($sample_api_data as $index => $offer) {
    echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 15px 0; border-radius: 10px;'>";
    echo "<h3>🎯 العرض " . ($index + 1) . ": " . htmlspecialchars($offer['title']) . "</h3>";
    
    // استخراج البيانات الأساسية
    $offer_name = $offer['title'];
    $offer_link = $offer['link'];
    $offer_image = $offer['creatives']['url'] ?? 'https://via.placeholder.com/300x200?text=No+Image';
    $offer_description = $offer['description'] ?? '';
    $offer_terms = $offer['conversion'] ?? '';
    
    echo "<strong>📝 البيانات الأساسية:</strong><br>";
    echo "- الاسم: " . htmlspecialchars($offer_name) . "<br>";
    echo "- الرابط: " . htmlspecialchars($offer_link) . "<br>";
    echo "- الصورة: " . htmlspecialchars($offer_image) . "<br>";
    echo "- الوصف: " . htmlspecialchars($offer_description) . "<br>";
    echo "- شروط التحويل: " . htmlspecialchars($offer_terms) . "<br>";
    
    // معالجة البلدان
    echo "<br><strong>🌍 معالجة البلدان:</strong><br>";
    
    $offer_countries = '';
    if (isset($offer['countries']) && is_array($offer['countries']) && !empty($offer['countries'])) {
        echo "- مصدر البلدان: حقل 'countries'<br>";
        $countries_array = array_map('trim', $offer['countries']);
        $offer_countries = implode(', ', $countries_array);
        echo "- البلدان الأصلية: [" . implode(', ', $offer['countries']) . "]<br>";
    } elseif (isset($offer['geo']) && is_array($offer['geo']) && !empty($offer['geo'])) {
        echo "- مصدر البلدان: حقل 'geo'<br>";
        $countries_array = array_map('trim', $offer['geo']);
        $offer_countries = implode(', ', $countries_array);
        echo "- البلدان الأصلية: [" . implode(', ', $offer['geo']) . "]<br>";
    } else {
        echo "- مصدر البلدان: غير محدد أو فارغ<br>";
        $offer_countries = 'ALL';
        echo "- البلدان الأصلية: []<br>";
    }
    
    echo "- البلدان بعد المعالجة: <strong style='color: #28a745;'>" . htmlspecialchars($offer_countries) . "</strong><br>";
    
    // محاكاة إدراج العرض
    echo "<br><strong>💾 محاكاة الإدراج:</strong><br>";
    echo "- سيتم إدراج العرض بالبيانات التالية:<br>";
    echo "  * الاسم: " . htmlspecialchars($offer_name) . "<br>";
    echo "  * البلدان المستهدفة: " . htmlspecialchars($offer_countries) . "<br>";
    
    // اختبار البحث
    echo "<br><strong>🔍 اختبار البحث:</strong><br>";
    $test_countries = ['US', 'EG', 'SA', 'FR'];
    
    foreach ($test_countries as $test_country) {
        $will_show = false;
        
        if ($offer_countries === 'ALL') {
            $will_show = true;
        } elseif (strpos($offer_countries, $test_country) !== false) {
            $will_show = true;
        }
        
        $status = $will_show ? '✅ سيظهر' : '❌ لن يظهر';
        echo "  * للبلد $test_country: $status<br>";
    }
    
    echo "</div>";
}

echo "<h2>3️⃣ اختبار دالة getOffersByCountry:</h2>";

try {
    // إدراج العروض التجريبية
    echo "<strong>📥 إدراج العروض التجريبية:</strong><br>";
    
    foreach ($sample_api_data as $offer) {
        $offer_name = $offer['title'];
        $offer_link = $offer['link'];
        $offer_image = $offer['creatives']['url'] ?? 'https://via.placeholder.com/300x200?text=No+Image';
        $offer_description = $offer['description'] ?? '';
        $offer_terms = $offer['conversion'] ?? '';
        
        // معالجة البلدان
        $offer_countries = '';
        if (isset($offer['countries']) && is_array($offer['countries']) && !empty($offer['countries'])) {
            $countries_array = array_map('trim', $offer['countries']);
            $offer_countries = implode(', ', $countries_array);
        } elseif (isset($offer['geo']) && is_array($offer['geo']) && !empty($offer['geo'])) {
            $countries_array = array_map('trim', $offer['geo']);
            $offer_countries = implode(', ', $countries_array);
        } else {
            $offer_countries = 'ALL';
        }
        
        // حذف العرض إذا كان موجوداً (للاختبار)
        $stmt = $pdo->prepare("DELETE FROM offers WHERE name = ?");
        $stmt->execute([$offer_name]);
        
        // إدراج العرض
        if (insertOffer($pdo, $offer_name, $offer_link, $offer_image, $offer_description, $offer_terms, $offer_countries)) {
            echo "✅ تم إدراج: " . htmlspecialchars($offer_name) . " (البلدان: $offer_countries)<br>";
        } else {
            echo "❌ فشل إدراج: " . htmlspecialchars($offer_name) . "<br>";
        }
    }
    
    echo "<br><strong>🔍 اختبار البحث حسب البلد:</strong><br>";
    
    $test_countries = ['US', 'EG', 'SA', 'FR', null];
    
    foreach ($test_countries as $country) {
        $country_name = $country ?: 'جميع البلدان';
        echo "<div style='background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>🌍 البحث للبلد: $country_name</strong><br>";
        
        $offers = getOffersByCountry($pdo, $country);
        echo "عدد العروض المجلبة: " . count($offers) . "<br>";
        
        if (!empty($offers)) {
            foreach ($offers as $offer) {
                echo "- " . htmlspecialchars($offer['name']) . " (البلدان: " . htmlspecialchars($offer['target_countries']) . ")<br>";
            }
        } else {
            echo "- لا توجد عروض متاحة<br>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px;'>";
    echo "<strong>❌ خطأ:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<h2>4️⃣ نصائح لحل المشاكل:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";
echo "<strong>💡 نصائح مهمة:</strong><br>";
echo "1. تأكد من أن حقل 'countries' أو 'geo' موجود في استجابة API<br>";
echo "2. إذا كان الحقل فارغاً، سيتم تعيين 'ALL' تلقائياً<br>";
echo "3. البحث يدعم البحث الجزئي والدقيق<br>";
echo "4. يمكن استخدام FIND_IN_SET للبحث في القوائم المفصولة بفواصل<br>";
echo "5. تأكد من تنظيف البيانات قبل الإدراج<br>";
echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='api_offers.php' style='color: #007bff;'>🌐 صفحة عروض API</a> - لجلب العروض الحقيقية</p>";
echo "<p><a href='manage_offers.php' style='color: #007bff;'>🗂️ إدارة العروض</a> - لمراجعة العروض المضافة</p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - لمشاهدة العروض</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
