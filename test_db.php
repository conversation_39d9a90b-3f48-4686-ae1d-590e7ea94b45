<?php
// ملف اختبار الاتصال بقاعدة البيانات
require_once 'config.php';

echo "<h1>🔍 اختبار الاتصال بقاعدة البيانات</h1>";

try {
    // اختبار الاتصال
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    // اختبار إنشاء الجداول
    echo "<h2>📋 حالة الجداول:</h2>";
    
    // فحص جدول المستخدمين
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ جدول المستخدمين موجود</p>";
        
        // عد المستخدمين
        $userCount = countUsers($pdo);
        echo "<p>👥 عدد المستخدمين: $userCount</p>";
    } else {
        echo "<p>❌ جدول المستخدمين غير موجود</p>";
    }
    
    // فحص جدول العروض
    $stmt = $pdo->query("SHOW TABLES LIKE 'offers'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ جدول العروض موجود</p>";
        
        // عد العروض
        $offerCount = countOffers($pdo);
        echo "<p>🎯 عدد العروض: $offerCount</p>";
    } else {
        echo "<p>❌ جدول العروض غير موجود</p>";
    }
    
    // اختبار إدراج بيانات تجريبية
    echo "<h2>🧪 اختبار العمليات:</h2>";
    
    // اختبار إدراج مستخدم تجريبي
    if (insertUser($pdo, 'Test', 'User')) {
        echo "<p>✅ تم إدراج مستخدم تجريبي بنجاح</p>";
    } else {
        echo "<p>❌ فشل في إدراج المستخدم التجريبي</p>";
    }
    
    // اختبار إدراج عرض تجريبي
    if (insertOffer($pdo, 'عرض تجريبي', 'https://example.com', 'https://via.placeholder.com/300x200')) {
        echo "<p>✅ تم إدراج عرض تجريبي بنجاح</p>";
    } else {
        echo "<p>❌ فشل في إدراج العرض التجريبي</p>";
    }
    
    echo "<h2>📊 الإحصائيات النهائية:</h2>";
    echo "<p>👥 إجمالي المستخدمين: " . countUsers($pdo) . "</p>";
    echo "<p>🎯 إجمالي العروض: " . countOffers($pdo) . "</p>";
    
    echo "<hr>";
    echo "<p><a href='index.php'>🏠 العودة للصفحة الرئيسية</a></p>";
    echo "<p><a href='admin.php'>⚙️ الذهاب للوحة التحكم</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>صحة بيانات الاتصال في ملف config.php</li>";
    echo "<li>أن قاعدة البيانات متاحة ومفعلة</li>";
    echo "<li>أن المستخدم له صلاحيات الوصول</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
