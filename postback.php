<?php
/*
 * CPAlead Postback Handler
 * يستقبل التحويلات من CPAlead ويحفظها في قاعدة البيانات
 */

require_once 'config.php';

// تسجيل جميع الطلبات للتشخيص
$log_data = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    'get_data' => $_GET,
    'post_data' => $_POST,
    'request_data' => $_REQUEST
];

// حفظ السجل في ملف للتشخيص
file_put_contents('postback_log.txt', json_encode($log_data, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);

// جلب إعدادات Postback
$settings = getPostbackSettings($pdo);

// التحقق من تفعيل Postback
if (!$settings || !$settings['is_enabled']) {
    http_response_code(403);
    die("Postback is disabled");
}

// التحقق من IP المسموح (اختياري)
$allowed_ip = $settings['whitelist_ip'] ?? '************';
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';

if (!empty($allowed_ip) && $client_ip !== $allowed_ip) {
    // في بيئة التطوير، قد نحتاج لتجاهل فحص IP
    // يمكن إزالة هذا التعليق في الإنتاج
    // http_response_code(403);
    // die("IP not whitelisted: $client_ip");
}

// التحقق من كلمة المرور (اختياري)
$required_password = $settings['postback_password'] ?? '';
$provided_password = $_REQUEST['password'] ?? '';

if (!empty($required_password) && $provided_password !== $required_password) {
    http_response_code(401);
    die("Invalid postback password");
}

// استخراج بيانات التحويل
$conversion_data = [
    'campaign_id' => $_REQUEST['campaign_id'] ?? '',
    'campaign_name' => $_REQUEST['campaign_name'] ?? '',
    'subid' => $_REQUEST['subid'] ?? '',
    'subid2' => $_REQUEST['subid2'] ?? '',
    'subid3' => $_REQUEST['subid3'] ?? '',
    'idfa' => $_REQUEST['idfa'] ?? '',
    'gaid' => $_REQUEST['gaid'] ?? '',
    'payout' => floatval($_REQUEST['payout'] ?? 0),
    'ip_address' => $_REQUEST['ip_address'] ?? $client_ip,
    'gateway_id' => $_REQUEST['gateway_id'] ?? '',
    'lead_id' => $_REQUEST['lead_id'] ?? '',
    'country_iso' => $_REQUEST['country_iso'] ?? '',
    'virtual_currency' => intval($_REQUEST['virtual_currency'] ?? 0)
];

// التحقق من وجود البيانات الأساسية
if (empty($conversion_data['subid']) && empty($conversion_data['lead_id'])) {
    http_response_code(400);
    die("Missing required parameters: subid or lead_id");
}

// حفظ التحويل في قاعدة البيانات
if (insertConversion($pdo, $conversion_data)) {
    // إرسال رد نجاح
    http_response_code(200);
    echo "OK - Conversion recorded successfully";
    
    // تسجيل النجاح
    $success_log = [
        'status' => 'success',
        'timestamp' => date('Y-m-d H:i:s'),
        'conversion_data' => $conversion_data
    ];
    file_put_contents('postback_success.txt', json_encode($success_log, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);
    
} else {
    // إرسال رد خطأ
    http_response_code(500);
    echo "ERROR - Failed to record conversion";
    
    // تسجيل الخطأ
    $error_log = [
        'status' => 'error',
        'timestamp' => date('Y-m-d H:i:s'),
        'conversion_data' => $conversion_data,
        'error' => 'Database insertion failed'
    ];
    file_put_contents('postback_errors.txt', json_encode($error_log, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);
}

// إضافة معلومات إضافية للاستجابة (للتشخيص)
if (isset($_GET['debug']) && $_GET['debug'] === '1') {
    echo "\n\nDEBUG INFO:\n";
    echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
    echo "Client IP: " . $client_ip . "\n";
    echo "Allowed IP: " . $allowed_ip . "\n";
    echo "Conversion Data: " . json_encode($conversion_data, JSON_PRETTY_PRINT) . "\n";
}
?>
