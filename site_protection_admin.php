<?php
session_start();
require_once 'config.php';

$message = '';

// معالجة تحديث إعدادات الحماية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_protection'])) {
    $is_locked = isset($_POST['is_locked']) ? 1 : 0;
    $password = cleanInput($_POST['password']);
    $lock_message = cleanInput($_POST['lock_message']);
    $allowed_ips = cleanInput($_POST['allowed_ips']);
    
    if (empty($lock_message)) {
        $lock_message = 'الموقع مغلق مؤقتاً للصيانة';
    }
    
    if (updateSiteProtection($pdo, $is_locked, $password, $lock_message, $allowed_ips)) {
        $message = '<div class="success">تم تحديث إعدادات الحماية بنجاح! ✅</div>';
        
        // إذا تم قفل الموقع، إلغاء جلسة فتح القفل
        if ($is_locked) {
            unset($_SESSION['site_unlocked']);
        }
    } else {
        $message = '<div class="error">فشل في تحديث إعدادات الحماية!</div>';
    }
}

// معالجة فتح القفل الطارئ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['emergency_unlock'])) {
    if (updateSiteProtection($pdo, false, '', 'الموقع مغلق مؤقتاً للصيانة', '')) {
        $message = '<div class="success">تم فتح الموقع بنجاح! ✅</div>';
        unset($_SESSION['site_unlocked']);
    } else {
        $message = '<div class="error">فشل في فتح الموقع!</div>';
    }
}

// جلب الإعدادات الحالية
$settings = getSiteProtectionSettings($pdo);
$user_ip = getUserIP();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة كلمة مرور الموقع</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .status-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .status-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .status-value {
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .status-locked {
            color: #dc3545;
        }
        
        .status-unlocked {
            color: #28a745;
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="password"]:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin-left: 10px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .checkbox-group label {
            margin-bottom: 0;
            margin-right: 10px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .warning-box {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .info-box {
            background: #e3f2fd;
            color: #1976d2;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .emergency-section {
            background: #ffebee;
            border: 2px solid #f44336;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .emergency-title {
            color: #c62828;
            font-size: 1.5em;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .home-link {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            font-size: 16px;
        }
        
        .home-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 إدارة كلمة مرور الموقع</h1>
            <p>تحكم في حماية الموقع بكلمة مرور للمستخدمين</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
                <a href="manage_offers.php" class="nav-link">🗂️ إدارة العروض</a>
                <a href="anti_duplicate_admin.php" class="nav-link">🛡️ منع التكرار</a>
                <a href="site_protection_admin.php" class="nav-link">🔑 كلمة مرور الموقع</a>
                <a href="postback_admin.php" class="nav-link">📊 Postback</a>
            </div>
        </div>
        
        <!-- حالة الموقع الحالية -->
        <div class="status-section">
            <div class="status-card">
                <div class="status-icon"><?php echo $settings['is_locked'] ? '🔑' : '🌐'; ?></div>
                <div class="status-title">حالة الموقع</div>
                <div class="status-value <?php echo $settings['is_locked'] ? 'status-locked' : 'status-unlocked'; ?>">
                    <?php echo $settings['is_locked'] ? 'محمي بكلمة مرور' : 'مفتوح للجميع'; ?>
                </div>
            </div>
            
            <div class="status-card">
                <div class="status-icon">🌐</div>
                <div class="status-title">IP الحالي</div>
                <div class="status-value" style="font-family: monospace; color: #667eea;">
                    <?php echo htmlspecialchars($user_ip); ?>
                </div>
            </div>
            
            <div class="status-card">
                <div class="status-icon">🔑</div>
                <div class="status-title">كلمة المرور</div>
                <div class="status-value" style="color: #6c757d;">
                    <?php echo !empty($settings['password_hash']) ? 'محددة' : 'غير محددة'; ?>
                </div>
            </div>
            
            <div class="status-card">
                <div class="status-icon">📋</div>
                <div class="status-title">IPs مسموحة</div>
                <div class="status-value" style="color: #6c757d;">
                    <?php 
                    if (!empty($settings['allowed_ips'])) {
                        $allowed_count = count(array_filter(array_map('trim', explode(',', $settings['allowed_ips']))));
                        echo $allowed_count . ' IP';
                    } else {
                        echo 'لا يوجد';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إلغاء كلمة المرور -->
        <?php if ($settings['is_locked']): ?>
        <div class="emergency-section">
            <h2 class="emergency-title">🌐 إلغاء كلمة المرور</h2>
            <div class="warning-box">
                <strong>⚠️ تحذير:</strong> هذا الإجراء سيجعل الموقع متاحاً للجميع بدون كلمة مرور.
            </div>
            <form method="POST">
                <button type="submit"
                        name="emergency_unlock"
                        class="btn-success"
                        onclick="return confirm('هل أنت متأكد من إلغاء كلمة المرور؟')">
                    🌐 إلغاء كلمة المرور
                </button>
            </form>
        </div>
        <?php endif; ?>
        
        <!-- إعدادات كلمة المرور -->
        <div class="form-section">
            <h2>⚙️ إعدادات كلمة المرور</h2>

            <div class="info-box">
                <strong>ℹ️ كيف يعمل النظام:</strong><br>
                • عند تفعيل الحماية، سيطلب من المستخدمين إدخال كلمة مرور<br>
                • بعد إدخال كلمة المرور الصحيحة، يمكن للمستخدم تصفح الموقع بحرية<br>
                • IPs المسموحة يمكنها الوصول مباشرة بدون كلمة مرور<br>
                • يمكن تخصيص رسالة الترحيب المعروضة للمستخدمين
            </div>
            
            <form method="POST">
                <div class="checkbox-group">
                    <input type="checkbox"
                           id="is_locked"
                           name="is_locked"
                           <?php echo $settings['is_locked'] ? 'checked' : ''; ?>>
                    <label for="is_locked">🔑 تفعيل حماية الموقع بكلمة مرور</label>
                </div>
                
                <div class="form-group">
                    <label for="password">🔑 كلمة مرور الموقع:</label>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           placeholder="أدخل كلمة مرور قوية..."
                           autocomplete="new-password">
                    <small style="color: #666; font-size: 14px;">
                        اتركها فارغة للاحتفاظ بكلمة المرور الحالية
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="lock_message">📝 رسالة الترحيب:</label>
                    <textarea id="lock_message"
                              name="lock_message"
                              rows="3"
                              placeholder="الرسالة التي ستظهر للمستخدمين عند طلب كلمة المرور"><?php echo htmlspecialchars($settings['lock_message']); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="allowed_ips">🌐 IPs مسموحة (اختياري):</label>
                    <textarea id="allowed_ips" 
                              name="allowed_ips" 
                              rows="3"
                              placeholder="***********, ********, <?php echo $user_ip; ?>"><?php echo htmlspecialchars($settings['allowed_ips']); ?></textarea>
                    <small style="color: #666; font-size: 14px;">
                        IPs مفصولة بفواصل، هذه العناوين يمكنها الوصول بدون كلمة مرور<br>
                        <strong>نصيحة:</strong> أضف IP الحالي (<?php echo $user_ip; ?>) لتجنب قفل نفسك خارج الموقع
                    </small>
                </div>
                
                <div class="warning-box">
                    <strong>⚠️ نصائح مهمة:</strong><br>
                    • تأكد من إضافة IP الخاص بك للقائمة المسموحة لتجنب إدخال كلمة المرور<br>
                    • احفظ كلمة المرور في مكان آمن وشاركها مع المستخدمين المخولين<br>
                    • يمكن إلغاء كلمة المرور في أي وقت من هذه الصفحة
                </div>
                
                <button type="submit" name="update_protection">💾 حفظ الإعدادات</button>
            </form>
        </div>

        <!-- معلومات إضافية -->
        <div class="form-section">
            <h2>💡 نصائح وإرشادات</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px;">
                <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                    <h4 style="color: #2e7d32; margin-bottom: 15px;">✅ أفضل الممارسات</h4>
                    <ul style="color: #666; line-height: 1.8;">
                        <li>استخدم كلمة مرور قوية ومعقدة</li>
                        <li>أضف IP الخاص بك دائماً للقائمة المسموحة</li>
                        <li>اختبر النظام قبل التطبيق النهائي</li>
                        <li>احتفظ بنسخة احتياطية من كلمة المرور</li>
                        <li>استخدم رسالة واضحة ومهذبة للمستخدمين</li>
                    </ul>
                </div>

                <div style="background: #fff3cd; padding: 20px; border-radius: 10px;">
                    <h4 style="color: #856404; margin-bottom: 15px;">⚠️ تحذيرات مهمة</h4>
                    <ul style="color: #666; line-height: 1.8;">
                        <li>لا تقفل الموقع بدون إضافة IP الخاص بك</li>
                        <li>تأكد من حفظ كلمة المرور في مكان آمن</li>
                        <li>اختبر الوصول من أجهزة مختلفة</li>
                        <li>لا تشارك كلمة المرور مع أشخاص غير موثوقين</li>
                        <li>استخدم الفتح الطارئ فقط عند الضرورة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- اختبار النظام -->
        <div class="form-section">
            <h2>🧪 اختبار النظام</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea;">
                    <h4 style="margin-bottom: 15px; color: #333;">🔍 فحص الحالة الحالية</h4>
                    <p style="color: #666; margin-bottom: 15px;">تحقق من حالة الموقع وإعداداته</p>
                    <div style="background: white; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                        <strong>الحالة:</strong> <?php echo $settings['is_locked'] ? '🔒 مقفل' : '🔓 مفتوح'; ?><br>
                        <strong>كلمة المرور:</strong> <?php echo !empty($settings['password_hash']) ? '✅ محددة' : '❌ غير محددة'; ?><br>
                        <strong>IPs مسموحة:</strong> <?php echo !empty($settings['allowed_ips']) ? '✅ محددة' : '❌ غير محددة'; ?>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">
                    <h4 style="margin-bottom: 15px; color: #333;">🌐 معلومات الشبكة</h4>
                    <p style="color: #666; margin-bottom: 15px;">معلومات الاتصال الحالي</p>
                    <div style="background: white; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px;">
                        <strong>IP الحالي:</strong> <?php echo $user_ip; ?><br>
                        <strong>User Agent:</strong><br>
                        <div style="word-break: break-all; color: #666; margin-top: 5px;">
                            <?php echo htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد', 0, 100)); ?>
                        </div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #dc3545;">
                    <h4 style="margin-bottom: 15px; color: #333;">🚨 إجراءات طارئة</h4>
                    <p style="color: #666; margin-bottom: 15px;">في حالة الطوارئ أو نسيان كلمة المرور</p>
                    <div style="background: white; padding: 15px; border-radius: 5px;">
                        <strong>الفتح الطارئ:</strong> متاح من هذه الصفحة<br>
                        <strong>الوصول المباشر:</strong> أضف IP للقائمة المسموحة<br>
                        <strong>قاعدة البيانات:</strong> يمكن التعديل مباشرة
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- زر عائم للصفحة الرئيسية -->
    <a href="index.php" class="home-link">🏠 الرئيسية</a>

    <script>
        // تحذير عند تفعيل القفل
        document.getElementById('is_locked').addEventListener('change', function() {
            if (this.checked) {
                const userIP = '<?php echo $user_ip; ?>';
                const allowedIPs = document.getElementById('allowed_ips').value;

                if (!allowedIPs.includes(userIP)) {
                    alert('⚠️ تحذير: IP الخاص بك (' + userIP + ') غير موجود في القائمة المسموحة!\n\nيُنصح بإضافته لتجنب قفل نفسك خارج الموقع.');
                }
            }
        });

        // إضافة IP الحالي تلقائياً
        function addCurrentIP() {
            const userIP = '<?php echo $user_ip; ?>';
            const allowedIPsField = document.getElementById('allowed_ips');
            const currentIPs = allowedIPsField.value.trim();

            if (!currentIPs.includes(userIP)) {
                if (currentIPs) {
                    allowedIPsField.value = currentIPs + ', ' + userIP;
                } else {
                    allowedIPsField.value = userIP;
                }
            }
        }

        // إضافة زر لإضافة IP الحالي
        const allowedIPsGroup = document.querySelector('label[for="allowed_ips"]').parentNode;
        const addIPButton = document.createElement('button');
        addIPButton.type = 'button';
        addIPButton.innerHTML = '➕ إضافة IP الحالي';
        addIPButton.style.marginTop = '10px';
        addIPButton.style.background = '#17a2b8';
        addIPButton.onclick = addCurrentIP;
        allowedIPsGroup.appendChild(addIPButton);

        // تأكيد قبل الحفظ إذا كان القفل مفعل
        document.querySelector('form').addEventListener('submit', function(e) {
            const isLocked = document.getElementById('is_locked').checked;
            const password = document.getElementById('password').value;
            const hasExistingPassword = <?php echo !empty($settings['password_hash']) ? 'true' : 'false'; ?>;

            if (isLocked && !password && !hasExistingPassword) {
                e.preventDefault();
                alert('❌ يجب تحديد كلمة مرور قبل قفل الموقع!');
                document.getElementById('password').focus();
                return false;
            }

            if (isLocked) {
                return confirm('هل أنت متأكد من قفل الموقع؟\n\nتأكد من:\n• حفظ كلمة المرور\n• إضافة IP الخاص بك للقائمة المسموحة\n• اختبار الوصول');
            }
        });
    </script>
</body>
</html>
