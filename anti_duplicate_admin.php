<?php
session_start();
require_once 'config.php';
require_once 'site_protection.php';

$message = '';

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    $is_enabled = isset($_POST['is_enabled']) ? 1 : 0;
    $block_duration_days = intval($_POST['block_duration_days']);
    $block_type = $_POST['block_type'];
    $whitelist_ips = cleanInput($_POST['whitelist_ips']);
    
    if ($block_duration_days < 1) {
        $block_duration_days = 1;
    }
    
    if (updateAntiDuplicateSettings($pdo, $is_enabled, $block_duration_days, $block_type, $whitelist_ips)) {
        $message = '<div class="success">تم تحديث إعدادات منع التكرار بنجاح! ✅</div>';
    } else {
        $message = '<div class="error">فشل في تحديث الإعدادات!</div>';
    }
}

// معالجة تنظيف البيانات القديمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['clean_old_data'])) {
    $days_to_keep = intval($_POST['days_to_keep']);
    if ($days_to_keep < 1) {
        $days_to_keep = 30;
    }
    
    if (cleanOldClicks($pdo, $days_to_keep)) {
        $message = '<div class="success">تم تنظيف البيانات القديمة بنجاح! ✅</div>';
    } else {
        $message = '<div class="error">فشل في تنظيف البيانات!</div>';
    }
}

// جلب الإعدادات الحالية
$settings = getAntiDuplicateSettings($pdo);
$stats = getClickStats($pdo);
$recent_clicks = getRecentClicks($pdo, 20);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة نظام منع التكرار</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .stat-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"], input[type="number"], select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="number"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin-left: 10px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .checkbox-group label {
            margin-bottom: 0;
            margin-right: 10px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .warning-box {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .clicks-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .clicks-table th,
        .clicks-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .clicks-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .clicks-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-enabled {
            color: #28a745;
            font-weight: 600;
        }
        
        .status-disabled {
            color: #dc3545;
            font-weight: 600;
        }
        
        .home-link {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            font-size: 16px;
        }
        
        .home-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ إدارة نظام منع التكرار</h1>
            <p>منع تكرار النقرات من نفس الـ IP</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
                <a href="manage_offers.php" class="nav-link">🗂️ إدارة العروض</a>
                <a href="anti_duplicate_admin.php" class="nav-link">🛡️ منع التكرار</a>
                <a href="postback_admin.php" class="nav-link">📊 Postback</a>
            </div>
        </div>
        
        <!-- إحصائيات النظام -->
        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-icon">👆</div>
                <div class="stat-number" style="color: #667eea;"><?php echo number_format($stats['total_clicks']); ?></div>
                <div class="stat-label">إجمالي النقرات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <div class="stat-number" style="color: #28a745;"><?php echo number_format($stats['today_clicks']); ?></div>
                <div class="stat-label">نقرات اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🌐</div>
                <div class="stat-number" style="color: #17a2b8;"><?php echo number_format($stats['unique_ips']); ?></div>
                <div class="stat-label">IPs فريدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🚫</div>
                <div class="stat-number" style="color: #dc3545;"><?php echo number_format($stats['blocked_clicks']); ?></div>
                <div class="stat-label">نقرات محجوبة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number" style="color: #6f42c1;"><?php echo number_format($stats['month_clicks']); ?></div>
                <div class="stat-label">نقرات الشهر</div>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إعدادات النظام -->
        <div class="form-section">
            <h2>⚙️ إعدادات نظام منع التكرار</h2>
            
            <div class="info-box">
                <strong>ℹ️ كيف يعمل النظام:</strong><br>
                • عند النقر على عرض، يتم تسجيل الـ IP والوقت<br>
                • إذا نقر نفس الـ IP مرة أخرى خلال المدة المحددة، لن يظهر له العرض<br>
                • يمكن اختيار منع لكل عرض منفصل أو منع عالمي لجميع العروض
            </div>
            
            <form method="POST">
                <div class="checkbox-group">
                    <input type="checkbox" 
                           id="is_enabled" 
                           name="is_enabled" 
                           <?php echo $settings['is_enabled'] ? 'checked' : ''; ?>>
                    <label for="is_enabled">تفعيل نظام منع التكرار</label>
                </div>
                
                <div class="form-group">
                    <label for="block_duration_days">مدة المنع (بالأيام):</label>
                    <input type="number" 
                           id="block_duration_days" 
                           name="block_duration_days" 
                           value="<?php echo $settings['block_duration_days']; ?>"
                           min="1" 
                           max="365"
                           placeholder="1">
                    <small style="color: #666; font-size: 14px;">
                        المدة التي سيتم منع الـ IP فيها من رؤية العروض بعد النقر
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="block_type">نوع المنع:</label>
                    <select id="block_type" name="block_type">
                        <option value="per_offer" <?php echo $settings['block_type'] === 'per_offer' ? 'selected' : ''; ?>>
                            لكل عرض منفصل (الأفضل)
                        </option>
                        <option value="global" <?php echo $settings['block_type'] === 'global' ? 'selected' : ''; ?>>
                            منع عالمي لجميع العروض
                        </option>
                    </select>
                    <small style="color: #666; font-size: 14px;">
                        • لكل عرض منفصل: يمكن للمستخدم رؤية عروض أخرى<br>
                        • منع عالمي: منع من جميع العروض بعد النقر على أي عرض
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="whitelist_ips">القائمة البيضاء للـ IPs (اختياري):</label>
                    <textarea id="whitelist_ips" 
                              name="whitelist_ips" 
                              rows="3"
                              placeholder="***********, ********, 127.0.0.1"><?php echo htmlspecialchars($settings['whitelist_ips']); ?></textarea>
                    <small style="color: #666; font-size: 14px;">
                        IPs مفصولة بفواصل، هذه العناوين لن تخضع لنظام منع التكرار
                    </small>
                </div>
                
                <button type="submit" name="update_settings">💾 حفظ الإعدادات</button>
            </form>
        </div>

        <!-- تنظيف البيانات القديمة -->
        <div class="form-section">
            <h2>🧹 تنظيف البيانات القديمة</h2>

            <div class="warning-box">
                <strong>⚠️ تحذير:</strong> هذا الإجراء سيحذف سجلات النقرات الأقدم من المدة المحددة ولا يمكن التراجع عنه.
            </div>

            <form method="POST">
                <div class="form-group">
                    <label for="days_to_keep">الاحتفاظ بالبيانات لآخر (أيام):</label>
                    <input type="number"
                           id="days_to_keep"
                           name="days_to_keep"
                           value="30"
                           min="1"
                           max="365"
                           placeholder="30">
                    <small style="color: #666; font-size: 14px;">
                        سيتم حذف جميع سجلات النقرات الأقدم من هذه المدة
                    </small>
                </div>

                <button type="submit"
                        name="clean_old_data"
                        class="btn-danger"
                        onclick="return confirm('هل أنت متأكد من حذف البيانات القديمة؟ هذا الإجراء لا يمكن التراجع عنه!')">
                    🗑️ تنظيف البيانات القديمة
                </button>
            </form>
        </div>

        <!-- حالة النظام الحالية -->
        <div class="form-section">
            <h2>📊 حالة النظام الحالية</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea;">
                    <h4 style="margin-bottom: 10px; color: #333;">🛡️ حالة النظام</h4>
                    <div class="<?php echo $settings['is_enabled'] ? 'status-enabled' : 'status-disabled'; ?>">
                        <?php echo $settings['is_enabled'] ? '✅ مفعل' : '❌ معطل'; ?>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">
                    <h4 style="margin-bottom: 10px; color: #333;">⏱️ مدة المنع</h4>
                    <div style="font-weight: 600; color: #28a745;">
                        <?php echo $settings['block_duration_days']; ?> يوم
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #17a2b8;">
                    <h4 style="margin-bottom: 10px; color: #333;">🎯 نوع المنع</h4>
                    <div style="font-weight: 600; color: #17a2b8;">
                        <?php echo $settings['block_type'] === 'per_offer' ? 'لكل عرض منفصل' : 'منع عالمي'; ?>
                    </div>
                </div>
            </div>

            <?php if (!empty($settings['whitelist_ips'])): ?>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="margin-bottom: 10px; color: #333;">📋 القائمة البيضاء:</h4>
                <div style="font-family: monospace; font-size: 14px; color: #666;">
                    <?php echo htmlspecialchars($settings['whitelist_ips']); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- سجل النقرات الأخيرة -->
        <?php if (!empty($recent_clicks)): ?>
        <div class="form-section">
            <h2>📋 سجل النقرات الأخيرة</h2>

            <table class="clicks-table">
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>العرض</th>
                        <th>IP Address</th>
                        <th>البلد</th>
                        <th>User Agent</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_clicks as $click): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($click['click_date'])); ?></td>
                        <td><?php echo htmlspecialchars($click['offer_name'] ?: 'عرض محذوف'); ?></td>
                        <td style="font-family: monospace;"><?php echo htmlspecialchars($click['ip_address']); ?></td>
                        <td><?php echo htmlspecialchars($click['country_code'] ?: '-'); ?></td>
                        <td style="font-size: 12px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                            <?php echo htmlspecialchars(substr($click['user_agent'], 0, 50)); ?>
                            <?php echo strlen($click['user_agent']) > 50 ? '...' : ''; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <div style="text-align: center; margin-top: 20px; color: #666;">
                عرض آخر <?php echo count($recent_clicks); ?> نقرة من إجمالي <?php echo number_format($stats['total_clicks']); ?> نقرة
            </div>
        </div>
        <?php endif; ?>

        <!-- معلومات إضافية -->
        <div class="form-section">
            <h2>💡 نصائح وإرشادات</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px;">
                <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                    <h4 style="color: #2e7d32; margin-bottom: 15px;">✅ أفضل الممارسات</h4>
                    <ul style="color: #666; line-height: 1.6;">
                        <li>استخدم "لكل عرض منفصل" للحصول على أفضل تجربة مستخدم</li>
                        <li>ابدأ بمدة منع قصيرة (1-3 أيام) واضبطها حسب الحاجة</li>
                        <li>أضف IPs الاختبار للقائمة البيضاء</li>
                        <li>راقب الإحصائيات بانتظام</li>
                    </ul>
                </div>

                <div style="background: #fff3cd; padding: 20px; border-radius: 10px;">
                    <h4 style="color: #856404; margin-bottom: 15px;">⚠️ تحذيرات مهمة</h4>
                    <ul style="color: #666; line-height: 1.6;">
                        <li>المنع العالمي قد يقلل من معدل التحويل</li>
                        <li>مدة المنع الطويلة قد تؤثر على المستخدمين الحقيقيين</li>
                        <li>تأكد من إضافة IP الخاص بك للقائمة البيضاء</li>
                        <li>نظف البيانات القديمة بانتظام لتوفير المساحة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- زر عائم للصفحة الرئيسية -->
    <a href="index.php" class="home-link">🏠 الرئيسية</a>
</body>
</html>
