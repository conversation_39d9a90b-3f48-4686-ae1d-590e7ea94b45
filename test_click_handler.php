<?php
// ملف اختبار معالج النقرات والتحويل
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🔗 اختبار معالج النقرات والتحويل</h1>";

// محاكاة معرف عرض للاختبار
$test_offer_id = isset($_GET['test_offer_id']) ? intval($_GET['test_offer_id']) : null;

echo "<h2>1️⃣ فحص الملفات والدوال المطلوبة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

// فحص وجود ملف click_handler.php
if (file_exists('click_handler.php')) {
    echo "✅ ملف click_handler.php موجود<br>";
} else {
    echo "❌ ملف click_handler.php غير موجود<br>";
}

// فحص الدوال المطلوبة
$required_functions = ['getUserIP', 'recordClick', 'isIPBlocked'];
foreach ($required_functions as $function) {
    if (function_exists($function)) {
        echo "✅ الدالة $function موجودة<br>";
    } else {
        echo "❌ الدالة $function غير موجودة<br>";
    }
}

echo "</div>";

echo "<h2>2️⃣ فحص قاعدة البيانات:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";

try {
    // فحص جدول العروض
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM offers");
    $offers_count = $stmt->fetch()['count'];
    echo "✅ جدول العروض: $offers_count عرض موجود<br>";
    
    // فحص جدول تتبع النقرات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM click_tracking");
    $clicks_count = $stmt->fetch()['count'];
    echo "✅ جدول تتبع النقرات: $clicks_count نقرة مسجلة<br>";
    
    // جلب عينة من العروض
    $stmt = $pdo->query("SELECT id, name, link FROM offers LIMIT 5");
    $sample_offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sample_offers)) {
        echo "<br><strong>📋 عينة من العروض المتاحة:</strong><br>";
        foreach ($sample_offers as $offer) {
            echo "<div style='margin: 5px 0; padding: 8px; background: white; border-radius: 5px;'>";
            echo "<strong>ID:</strong> {$offer['id']} - ";
            echo "<strong>الاسم:</strong> " . htmlspecialchars($offer['name']) . " - ";
            echo "<strong>الرابط:</strong> <a href='" . htmlspecialchars($offer['link']) . "' target='_blank'>" . htmlspecialchars(substr($offer['link'], 0, 50)) . "...</a>";
            echo "<div style='margin-top: 5px;'>";
            echo "<a href='?test_offer_id={$offer['id']}' style='color: #007bff; margin-left: 5px; font-size: 12px;'>[فحص تفصيلي]</a>";
            echo "<a href='test_redirect.php?offer_id={$offer['id']}&test=1' style='color: #28a745; margin-left: 5px; font-size: 12px;'>[اختبار التحويل]</a>";
            echo "<a href='click_handler.php?offer_id={$offer['id']}' target='_blank' style='color: #dc3545; margin-left: 5px; font-size: 12px;'>[تحويل مباشر]</a>";
            echo "</div>";
            echo "</div>";
        }
    } else {
        echo "⚠️ لا توجد عروض في قاعدة البيانات<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "</div>";

if ($test_offer_id) {
    echo "<h2>3️⃣ اختبار العرض ID: $test_offer_id</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";
    
    try {
        // جلب معلومات العرض
        $stmt = $pdo->prepare("SELECT * FROM offers WHERE id = ?");
        $stmt->execute([$test_offer_id]);
        $offer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($offer) {
            echo "<strong>✅ تم العثور على العرض:</strong><br>";
            echo "<strong>الاسم:</strong> " . htmlspecialchars($offer['name']) . "<br>";
            echo "<strong>الرابط:</strong> " . htmlspecialchars($offer['link']) . "<br>";
            echo "<strong>البلدان المستهدفة:</strong> " . htmlspecialchars($offer['target_countries'] ?: 'غير محدد') . "<br>";
            echo "<strong>تاريخ الإضافة:</strong> " . ($offer['created_date'] ?? 'غير محدد') . "<br>";
            
            // فحص صحة الرابط
            echo "<br><strong>🔍 فحص صحة الرابط:</strong><br>";
            $url = $offer['link'];
            
            if (filter_var($url, FILTER_VALIDATE_URL)) {
                echo "✅ الرابط صحيح من ناحية التنسيق<br>";
                
                // محاولة فحص الرابط
                $headers = @get_headers($url, 1);
                if ($headers) {
                    $status_code = substr($headers[0], 9, 3);
                    if ($status_code == '200') {
                        echo "✅ الرابط يعمل (HTTP 200)<br>";
                    } else {
                        echo "⚠️ الرابط يرجع كود: $status_code<br>";
                    }
                } else {
                    echo "⚠️ لا يمكن فحص حالة الرابط (قد يكون محجوب أو بطيء)<br>";
                }
            } else {
                echo "❌ الرابط غير صحيح<br>";
            }
            
            // محاكاة عملية التحويل
            echo "<br><strong>🔄 محاكاة عملية التحويل:</strong><br>";
            
            $user_ip = getUserIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            echo "- IP المستخدم: $user_ip<br>";
            echo "- User Agent: " . htmlspecialchars(substr($user_agent, 0, 100)) . "...<br>";
            
            // فحص نظام منع التكرار
            $is_blocked = isIPBlocked($pdo, $test_offer_id, $user_ip);
            if ($is_blocked) {
                echo "🚫 هذا الـ IP محجوب من هذا العرض<br>";
            } else {
                echo "✅ الـ IP غير محجوب، يمكن المتابعة<br>";
                
                // تسجيل النقرة (للاختبار)
                $click_recorded = recordClick($pdo, $test_offer_id, $user_ip, $user_agent, 'TEST');
                if ($click_recorded) {
                    echo "✅ تم تسجيل النقرة بنجاح<br>";
                } else {
                    echo "❌ فشل في تسجيل النقرة<br>";
                }
            }
            
            // رابط التحويل الفعلي
            echo "<br><strong>🚀 رابط التحويل:</strong><br>";
            echo "<a href='click_handler.php?offer_id=$test_offer_id' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;'>🔗 اختبار التحويل الفعلي</a><br>";
            echo "<small style='color: #666;'>سيتم فتح الرابط في نافذة جديدة</small>";
            
        } else {
            echo "❌ لم يتم العثور على العرض بالمعرف: $test_offer_id<br>";
        }
        
    } catch (PDOException $e) {
        echo "❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
    
    echo "</div>";
}

echo "<h2>4️⃣ إحصائيات النقرات:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";

try {
    $stats = getClickStats($pdo);
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;'>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #007bff;'>{$stats['total_clicks']}</div>";
    echo "<div>إجمالي النقرات</div>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #28a745;'>{$stats['today_clicks']}</div>";
    echo "<div>نقرات اليوم</div>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #17a2b8;'>{$stats['unique_ips']}</div>";
    echo "<div>IPs فريدة</div>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #dc3545;'>{$stats['blocked_clicks']}</div>";
    echo "<div>نقرات محجوبة</div>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب الإحصائيات: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "</div>";

echo "<h2>5️⃣ آخر النقرات:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";

try {
    $recent_clicks = getRecentClicks($pdo, 10);
    
    if (!empty($recent_clicks)) {
        echo "<table style='width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden;'>";
        echo "<thead style='background: #007bff; color: white;'>";
        echo "<tr><th style='padding: 10px; text-align: right;'>العرض</th><th style='padding: 10px; text-align: right;'>IP</th><th style='padding: 10px; text-align: right;'>البلد</th><th style='padding: 10px; text-align: right;'>التاريخ</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($recent_clicks as $click) {
            echo "<tr style='border-bottom: 1px solid #eee;'>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($click['offer_name'] ?: 'عرض محذوف') . "</td>";
            echo "<td style='padding: 8px; font-family: monospace;'>" . htmlspecialchars($click['ip_address']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($click['country_code'] ?: 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . date('Y-m-d H:i', strtotime($click['click_date'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
    } else {
        echo "📭 لا توجد نقرات مسجلة بعد<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب النقرات: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "</div>";

echo "<h2>6️⃣ تشخيص مشاكل التحويل:</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";

echo "<strong>🔍 فحص المشاكل المحتملة:</strong><br><br>";

// فحص إعدادات PHP
echo "<strong>إعدادات PHP:</strong><br>";
echo "- display_errors: " . (ini_get('display_errors') ? 'مفعل' : 'معطل') . "<br>";
echo "- error_reporting: " . error_reporting() . "<br>";
echo "- allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'مفعل' : 'معطل') . "<br>";
echo "- max_execution_time: " . ini_get('max_execution_time') . " ثانية<br>";

// فحص الاتصال بالإنترنت
echo "<br><strong>فحص الاتصال:</strong><br>";
$test_url = "https://httpbin.org/status/200";
$context = stream_context_create(['http' => ['timeout' => 5]]);
$response = @file_get_contents($test_url, false, $context);
if ($response !== false) {
    echo "✅ الاتصال بالإنترنت يعمل<br>";
} else {
    echo "❌ مشكلة في الاتصال بالإنترنت<br>";
}

// فحص headers
echo "<br><strong>فحص Headers:</strong><br>";
if (!headers_sent()) {
    echo "✅ لم يتم إرسال headers بعد<br>";
} else {
    echo "⚠️ تم إرسال headers مسبقاً<br>";
}

// فحص ملف click_handler.php
echo "<br><strong>فحص ملف التحويل:</strong><br>";
$click_handler_content = @file_get_contents('click_handler.php');
if ($click_handler_content !== false) {
    echo "✅ ملف click_handler.php قابل للقراءة<br>";

    // فحص وجود أخطاء syntax
    $temp_file = tempnam(sys_get_temp_dir(), 'php_check');
    file_put_contents($temp_file, $click_handler_content);

    $output = [];
    $return_var = 0;
    exec("php -l $temp_file 2>&1", $output, $return_var);

    if ($return_var === 0) {
        echo "✅ لا توجد أخطاء syntax في click_handler.php<br>";
    } else {
        echo "❌ أخطاء syntax في click_handler.php:<br>";
        echo "<pre style='background: #fff; padding: 10px; border-radius: 5px; font-size: 12px;'>" . implode("\n", $output) . "</pre>";
    }

    unlink($temp_file);
} else {
    echo "❌ لا يمكن قراءة ملف click_handler.php<br>";
}

// فحص صلاحيات الملفات
echo "<br><strong>فحص الصلاحيات:</strong><br>";
$files_to_check = ['click_handler.php', 'config.php', 'index.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_str = substr(sprintf('%o', $perms), -4);
        echo "- $file: $perms_str " . (is_readable($file) ? '✅' : '❌') . "<br>";
    } else {
        echo "- $file: غير موجود ❌<br>";
    }
}

echo "</div>";

echo "<h2>7️⃣ دليل حل المشاكل:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";

echo "<strong>🚨 المشاكل الشائعة وحلولها:</strong><br><br>";

echo "<strong>1. التحويل لا يعمل نهائياً:</strong><br>";
echo "- فحص وجود أخطاء PHP في click_handler.php<br>";
echo "- التأكد من صحة رابط العرض<br>";
echo "- فحص اتصال قاعدة البيانات<br>";
echo "- التأكد من وجود العرض في قاعدة البيانات<br><br>";

echo "<strong>2. يتم التحويل لكن لا يتم تسجيل النقرة:</strong><br>";
echo "- فحص جدول click_tracking في قاعدة البيانات<br>";
echo "- التأكد من صلاحيات الكتابة في قاعدة البيانات<br>";
echo "- فحص دالة recordClick()<br><br>";

echo "<strong>3. رسالة 'IP محجوب':</strong><br>";
echo "- فحص إعدادات نظام منع التكرار<br>";
echo "- تعطيل النظام مؤقتاً للاختبار<br>";
echo "- إضافة IP للقائمة البيضاء<br>";
echo "- حذف السجلات القديمة من click_tracking<br><br>";

echo "<strong>4. خطأ 'العرض غير موجود':</strong><br>";
echo "- التأكد من وجود العرض في جدول offers<br>";
echo "- فحص معرف العرض في الرابط<br>";
echo "- التأكد من عدم حذف العرض<br><br>";

echo "<strong>🔧 أدوات الاختبار:</strong><br>";
echo "- <a href='test_redirect.php?offer_id=1&test=1' style='color: #0c5460;'>اختبار التحويل التفصيلي</a><br>";
echo "- استخدام وضع التصفح الخفي<br>";
echo "- اختبار من أجهزة مختلفة<br>";
echo "- فحص سجل أخطاء الخادم<br>";
echo "- استخدام أدوات المطور في المتصفح<br><br>";

echo "<strong>📞 للدعم الفني:</strong><br>";
echo "- احفظ رسالة الخطأ كاملة<br>";
echo "- سجل خطوات إعادة إنتاج المشكلة<br>";
echo "- اذكر معرف العرض والوقت<br>";
echo "- أرفق لقطة شاشة إن أمكن<br>";

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - لاختبار النقر على العروض</p>";
echo "<p><a href='manage_offers.php' style='color: #007bff;'>🗂️ إدارة العروض</a> - لإضافة أو تعديل العروض</p>";
echo "<p><a href='anti_duplicate_admin.php' style='color: #007bff;'>🛡️ منع التكرار</a> - لإدارة إعدادات الحجب</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - للإحصائيات العامة</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

table {
    font-size: 14px;
}

th, td {
    border: 1px solid #ddd;
}
</style>
