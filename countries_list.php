<?php
// صفحة عرض جميع الدول المتاحة
require_once 'config.php';

echo "<h1>🌍 قائمة جميع الدول المتاحة</h1>";

// الحصول على جميع الدول من الدالة
$all_countries = [
    // أمريكا الشمالية
    'US' => 'الولايات المتحدة',
    'CA' => 'كندا',
    'MX' => 'المكسيك',
    
    // أوروبا
    'GB' => 'المملكة المتحدة',
    'DE' => 'ألمانيا',
    'FR' => 'فرنسا',
    'IT' => 'إيطاليا',
    'ES' => 'إسبانيا',
    'NL' => 'هولندا',
    'BE' => 'بلجيكا',
    'CH' => 'سويسرا',
    'AT' => 'النمسا',
    'SE' => 'السويد',
    'NO' => 'النرويج',
    'DK' => 'الدنمارك',
    'FI' => 'فنلندا',
    'IE' => 'أيرلندا',
    'PT' => 'البرتغال',
    'GR' => 'اليونان',
    'PL' => 'بولندا',
    'CZ' => 'التشيك',
    'HU' => 'المجر',
    'RO' => 'رومانيا',
    'BG' => 'بلغاريا',
    'HR' => 'كرواتيا',
    'SI' => 'سلوفينيا',
    'SK' => 'سلوفاكيا',
    'EE' => 'إستونيا',
    'LV' => 'لاتفيا',
    'LT' => 'ليتوانيا',
    
    // آسيا والمحيط الهادئ
    'AU' => 'أستراليا',
    'NZ' => 'نيوزيلندا',
    'JP' => 'اليابان',
    'KR' => 'كوريا الجنوبية',
    'SG' => 'سنغافورة',
    'HK' => 'هونغ كونغ',
    'TW' => 'تايوان',
    'MY' => 'ماليزيا',
    'TH' => 'تايلاند',
    'PH' => 'الفلبين',
    'ID' => 'إندونيسيا',
    'VN' => 'فيتنام',
    'IN' => 'الهند',
    'CN' => 'الصين',
    
    // الشرق الأوسط وأفريقيا
    'AE' => 'الإمارات العربية المتحدة',
    'SA' => 'السعودية',
    'QA' => 'قطر',
    'KW' => 'الكويت',
    'BH' => 'البحرين',
    'OM' => 'عمان',
    'JO' => 'الأردن',
    'LB' => 'لبنان',
    'EG' => 'مصر',
    'MA' => 'المغرب',
    'TN' => 'تونس',
    'DZ' => 'الجزائر',
    'IL' => 'إسرائيل',
    'TR' => 'تركيا',
    'ZA' => 'جنوب أفريقيا',
    'NG' => 'نيجيريا',
    'KE' => 'كينيا',
    'GH' => 'غانا',
    
    // أمريكا الجنوبية
    'BR' => 'البرازيل',
    'AR' => 'الأرجنتين',
    'CL' => 'تشيلي',
    'CO' => 'كولومبيا',
    'PE' => 'بيرو',
    'VE' => 'فنزويلا',
    'UY' => 'أوروغواي',
    'EC' => 'الإكوادور'
];

// تجميع الدول حسب المنطقة
$regions = [
    'أمريكا الشمالية' => ['US', 'CA', 'MX'],
    'أوروبا' => ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'CH', 'AT', 'SE', 'NO', 'DK', 'FI', 'IE', 'PT', 'GR', 'PL', 'CZ', 'HU', 'RO', 'BG', 'HR', 'SI', 'SK', 'EE', 'LV', 'LT'],
    'آسيا والمحيط الهادئ' => ['AU', 'NZ', 'JP', 'KR', 'SG', 'HK', 'TW', 'MY', 'TH', 'PH', 'ID', 'VN', 'IN', 'CN'],
    'الشرق الأوسط وأفريقيا' => ['AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB', 'EG', 'MA', 'TN', 'DZ', 'IL', 'TR', 'ZA', 'NG', 'KE', 'GH'],
    'أمريكا الجنوبية' => ['BR', 'AR', 'CL', 'CO', 'PE', 'VE', 'UY', 'EC']
];

echo "<h2>📊 إحصائيات:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

$total_countries = count($all_countries);
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 2em; font-weight: bold; color: #1976d2;'>$total_countries</div>";
echo "<div style='color: #666;'>إجمالي الدول</div>";
echo "</div>";

foreach ($regions as $region_name => $countries) {
    $count = count($countries);
    echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center;'>";
    echo "<div style='font-size: 1.5em; font-weight: bold; color: #7b1fa2;'>$count</div>";
    echo "<div style='color: #666; font-size: 14px;'>$region_name</div>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";

// عرض الدول مجمعة حسب المنطقة
foreach ($regions as $region_name => $countries) {
    echo "<h2>🌍 $region_name (" . count($countries) . " دولة):</h2>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($countries as $code) {
        $name = $all_countries[$code];
        echo "<div style='background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); border-left: 4px solid #667eea;'>";
        echo "<div style='font-weight: 600; color: #333; margin-bottom: 5px;'>$name</div>";
        echo "<div style='color: #666; font-size: 14px; font-family: monospace;'>$code</div>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "<hr>";

// قائمة سريعة للنسخ
echo "<h2>📋 قائمة سريعة للنسخ:</h2>";

echo "<h3>🔤 رموز البلدان فقط:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; margin: 10px 0;'>";
echo implode(', ', array_keys($all_countries));
echo "</div>";

echo "<h3>📝 قائمة كاملة (رمز - اسم):</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; max-height: 300px; overflow-y: auto;'>";
foreach ($all_countries as $code => $name) {
    echo "$code - $name<br>";
}
echo "</div>";

echo "<hr>";

// أمثلة للاستخدام
echo "<h2>💡 أمثلة للاستخدام:</h2>";

$examples = [
    'الولايات المتحدة فقط' => 'US',
    'الدول الناطقة بالإنجليزية' => 'US, GB, CA, AU, NZ',
    'أوروبا الغربية' => 'GB, DE, FR, IT, ES, NL',
    'دول الخليج العربي' => 'AE, SA, QA, KW, BH, OM',
    'الدول العربية' => 'AE, SA, QA, KW, BH, OM, JO, LB, EG, MA, TN, DZ',
    'آسيا الشرقية' => 'JP, KR, CN, HK, TW',
    'أمريكا الشمالية' => 'US, CA, MX',
    'جميع البلدان' => 'ALL'
];

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 15px; margin: 20px 0;'>";

foreach ($examples as $description => $codes) {
    echo "<div style='background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
    echo "<div style='font-weight: 600; color: #333; margin-bottom: 10px;'>$description:</div>";
    echo "<div style='background: #e8f5e8; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 14px; color: #2e7d32;'>";
    echo $codes;
    echo "</div>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='api_offers.php' style='color: #007bff;'>🌐 جلب العروض من API</a> - استخدم هذه الرموز لفلترة العروض</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - أضف البلدان المستهدفة للعروض</p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - شاهد العروض المفلترة حسب بلدك</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1400px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    color: #e83e8c;
}
</style>
