<?php
// نظام حماية الموقع - يتم تضمينه في جميع الصفحات
if (!isset($pdo)) {
    require_once 'config.php';
}

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$user_ip = getUserIP();

// فحص حالة الموقع
$is_site_locked = isSiteLocked($pdo);
$is_ip_allowed = isIPAllowed($pdo, $user_ip);
$is_unlocked_by_password = isset($_SESSION['site_unlocked']) && $_SESSION['site_unlocked'] === true;

// إذا كان الموقع مقفل والمستخدم غير مخول
if ($is_site_locked && !$is_ip_allowed && !$is_unlocked_by_password) {
    
    // معالجة محاولة فتح القفل
    $unlock_error = '';
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_password'])) {
        $password = $_POST['unlock_password'];
        
        if (verifyUnlockPassword($pdo, $password)) {
            $_SESSION['site_unlocked'] = true;
            header('Location: ' . $_SERVER['REQUEST_URI']);
            exit;
        } else {
            $unlock_error = 'كلمة المرور غير صحيحة!';
        }
    }
    
    // جلب رسالة القفل
    $protection_settings = getSiteProtectionSettings($pdo);
    $lock_message = $protection_settings['lock_message'] ?: 'الموقع مغلق مؤقتاً للصيانة';
    
    // عرض صفحة القفل
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الموقع مقفل</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            
            .lock-container {
                background: white;
                padding: 50px;
                border-radius: 20px;
                box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
                text-align: center;
                max-width: 500px;
                width: 100%;
                animation: fadeIn 0.5s ease-in;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .lock-icon {
                font-size: 4em;
                color: #667eea;
                margin-bottom: 30px;
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }
            
            .lock-title {
                color: #333;
                font-size: 2.5em;
                margin-bottom: 20px;
                font-weight: 700;
            }
            
            .lock-message {
                color: #666;
                font-size: 1.2em;
                line-height: 1.6;
                margin-bottom: 40px;
            }
            
            .unlock-form {
                margin-top: 30px;
            }
            
            .form-group {
                margin-bottom: 20px;
                text-align: right;
            }
            
            label {
                display: block;
                margin-bottom: 8px;
                color: #555;
                font-weight: 600;
            }
            
            input[type="password"] {
                width: 100%;
                padding: 15px 20px;
                border: 2px solid #ddd;
                border-radius: 10px;
                font-size: 16px;
                transition: border-color 0.3s ease;
                text-align: center;
            }
            
            input[type="password"]:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
            }
            
            .unlock-btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px 40px;
                border: none;
                border-radius: 10px;
                font-size: 18px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                width: 100%;
                margin-top: 10px;
            }
            
            .unlock-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            }
            
            .error-message {
                background: #f8d7da;
                color: #721c24;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border: 1px solid #f5c6cb;
                animation: shake 0.5s ease-in-out;
            }
            
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            
            .info-box {
                background: #e3f2fd;
                color: #1976d2;
                padding: 15px;
                border-radius: 8px;
                margin-top: 30px;
                font-size: 14px;
                border-left: 4px solid #2196f3;
            }
            
            .ip-info {
                background: #f8f9fa;
                padding: 10px;
                border-radius: 5px;
                margin-top: 20px;
                font-size: 12px;
                color: #666;
                font-family: monospace;
            }
            
            @media (max-width: 768px) {
                .lock-container {
                    padding: 30px 20px;
                }
                
                .lock-title {
                    font-size: 2em;
                }
                
                .lock-icon {
                    font-size: 3em;
                }
            }
        </style>
    </head>
    <body>
        <div class="lock-container">
            <div class="lock-icon">🔒</div>
            <h1 class="lock-title">الموقع مقفل</h1>
            <p class="lock-message"><?php echo htmlspecialchars($lock_message); ?></p>
            
            <?php if (!empty($unlock_error)): ?>
                <div class="error-message">❌ <?php echo htmlspecialchars($unlock_error); ?></div>
            <?php endif; ?>
            
            <form method="POST" class="unlock-form">
                <div class="form-group">
                    <label for="unlock_password">🔑 أدخل كلمة المرور لفتح الموقع:</label>
                    <input type="password" 
                           id="unlock_password" 
                           name="unlock_password" 
                           placeholder="كلمة المرور..."
                           required
                           autofocus>
                </div>
                
                <button type="submit" class="unlock-btn">🔓 فتح الموقع</button>
            </form>
            
            <div class="info-box">
                <strong>ℹ️ معلومة:</strong><br>
                إذا كنت مدير الموقع، يمكنك الوصول لإعدادات الحماية من لوحة التحكم
            </div>
            
            <div class="ip-info">
                IP الخاص بك: <?php echo htmlspecialchars($user_ip); ?>
            </div>
        </div>
        
        <script>
            // تركيز تلقائي على حقل كلمة المرور
            document.getElementById('unlock_password').focus();
            
            // إضافة تأثير الكتابة
            document.getElementById('unlock_password').addEventListener('input', function() {
                if (this.value.length > 0) {
                    this.style.borderColor = '#28a745';
                } else {
                    this.style.borderColor = '#ddd';
                }
            });
            
            // منع النقر بالزر الأيمن والاختصارات
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
            
            document.addEventListener('keydown', function(e) {
                // منع F12, Ctrl+Shift+I, Ctrl+U
                if (e.key === 'F12' || 
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.key === 'u')) {
                    e.preventDefault();
                }
            });
        </script>
    </body>
    </html>
    <?php
    exit;
}
?>
