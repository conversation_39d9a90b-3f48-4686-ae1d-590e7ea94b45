<?php
// ملف اختبار التحويل المباشر
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

$offer_id = isset($_GET['offer_id']) ? intval($_GET['offer_id']) : null;
$test_mode = isset($_GET['test']) && $_GET['test'] === '1';

if (!$offer_id) {
    echo "<h1>❌ معرف العرض مطلوب</h1>";
    echo "<p><a href='test_click_handler.php'>العودة لصفحة الاختبار</a></p>";
    exit;
}

// جلب معلومات العرض
try {
    $stmt = $pdo->prepare("SELECT * FROM offers WHERE id = ?");
    $stmt->execute([$offer_id]);
    $offer = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$offer) {
        echo "<h1>❌ العرض غير موجود</h1>";
        echo "<p>العرض بالمعرف $offer_id غير موجود في قاعدة البيانات</p>";
        echo "<p><a href='test_click_handler.php'>العودة لصفحة الاختبار</a></p>";
        exit;
    }
} catch(PDOException $e) {
    echo "<h1>❌ خطأ في قاعدة البيانات</h1>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

if ($test_mode) {
    // وضع الاختبار - عرض المعلومات بدلاً من التحويل
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🧪 اختبار التحويل - <?php echo htmlspecialchars($offer['name']); ?></title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 50px auto;
                padding: 20px;
                background: #f5f5f5;
                line-height: 1.6;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #007bff;
            }
            .info-section {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                border-left: 4px solid #007bff;
            }
            .success {
                background: #d4edda;
                color: #155724;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #c3e6cb;
                margin: 15px 0;
            }
            .warning {
                background: #fff3cd;
                color: #856404;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #ffeaa7;
                margin: 15px 0;
            }
            .error {
                background: #f8d7da;
                color: #721c24;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #f5c6cb;
                margin: 15px 0;
            }
            .btn {
                display: inline-block;
                padding: 12px 25px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 25px;
                margin: 10px 5px;
                transition: all 0.3s ease;
            }
            .btn:hover {
                background: #0056b3;
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
            }
            .btn-success {
                background: #28a745;
            }
            .btn-success:hover {
                background: #1e7e34;
            }
            .btn-warning {
                background: #ffc107;
                color: #212529;
            }
            .btn-warning:hover {
                background: #e0a800;
                color: #212529;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🧪 اختبار التحويل</h1>
                <p>فحص شامل لعملية التحويل قبل التنفيذ</p>
            </div>
            
            <div class="info-section">
                <h2>📋 معلومات العرض</h2>
                <p><strong>المعرف:</strong> <?php echo $offer['id']; ?></p>
                <p><strong>الاسم:</strong> <?php echo htmlspecialchars($offer['name']); ?></p>
                <p><strong>الرابط:</strong> <a href="<?php echo htmlspecialchars($offer['link']); ?>" target="_blank"><?php echo htmlspecialchars($offer['link']); ?></a></p>
                <p><strong>البلدان المستهدفة:</strong> <?php echo htmlspecialchars($offer['target_countries'] ?: 'جميع البلدان'); ?></p>
                <p><strong>تاريخ الإضافة:</strong> <?php echo $offer['created_date'] ?? 'غير محدد'; ?></p>
            </div>
            
            <?php
            // فحص صحة الرابط
            $link_valid = filter_var($offer['link'], FILTER_VALIDATE_URL);
            if ($link_valid) {
                echo "<div class='success'>✅ الرابط صحيح من ناحية التنسيق</div>";
                
                // محاولة فحص حالة الرابط
                $headers = @get_headers($offer['link'], 1);
                if ($headers) {
                    $status_code = substr($headers[0], 9, 3);
                    if ($status_code == '200') {
                        echo "<div class='success'>✅ الرابط يعمل بشكل صحيح (HTTP 200)</div>";
                    } else {
                        echo "<div class='warning'>⚠️ الرابط يرجع كود HTTP: $status_code</div>";
                    }
                } else {
                    echo "<div class='warning'>⚠️ لا يمكن فحص حالة الرابط (قد يكون محجوب أو بطيء)</div>";
                }
            } else {
                echo "<div class='error'>❌ الرابط غير صحيح</div>";
            }
            
            // فحص معلومات المستخدم
            $user_ip = getUserIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            echo "<div class='info-section'>";
            echo "<h2>👤 معلومات المستخدم</h2>";
            echo "<p><strong>IP:</strong> $user_ip</p>";
            echo "<p><strong>User Agent:</strong> " . htmlspecialchars(substr($user_agent, 0, 100)) . "...</p>";
            echo "</div>";
            
            // فحص نظام منع التكرار
            $is_blocked = isIPBlocked($pdo, $offer['id'], $user_ip);
            if ($is_blocked) {
                echo "<div class='error'>🚫 هذا الـ IP محجوب من هذا العرض حسب نظام منع التكرار</div>";
            } else {
                echo "<div class='success'>✅ الـ IP غير محجوب، يمكن المتابعة</div>";
            }
            
            // فحص إعدادات منع التكرار
            $anti_duplicate_settings = getAntiDuplicateSettings($pdo);
            echo "<div class='info-section'>";
            echo "<h2>🛡️ إعدادات منع التكرار</h2>";
            echo "<p><strong>مفعل:</strong> " . ($anti_duplicate_settings['is_enabled'] ? 'نعم' : 'لا') . "</p>";
            echo "<p><strong>مدة المنع:</strong> " . $anti_duplicate_settings['block_duration_days'] . " يوم</p>";
            echo "<p><strong>نوع المنع:</strong> " . $anti_duplicate_settings['block_type'] . "</p>";
            echo "</div>";
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <h2>🚀 خيارات التحويل</h2>
                
                <?php if ($link_valid && !$is_blocked): ?>
                    <a href="click_handler.php?offer_id=<?php echo $offer['id']; ?>" class="btn btn-success">
                        🔗 تحويل فعلي (عبر click_handler.php)
                    </a>
                    
                    <a href="<?php echo htmlspecialchars($offer['link']); ?>" target="_blank" class="btn btn-warning">
                        🌐 تحويل مباشر (بدون تسجيل)
                    </a>
                <?php else: ?>
                    <div class="error">❌ لا يمكن التحويل بسبب المشاكل المذكورة أعلاه</div>
                <?php endif; ?>
                
                <a href="test_click_handler.php" class="btn">
                    🔙 العودة للاختبار
                </a>
            </div>
            
            <div class="info-section">
                <h2>📊 إحصائيات سريعة</h2>
                <?php
                try {
                    // عدد النقرات لهذا العرض
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM click_tracking WHERE offer_id = ?");
                    $stmt->execute([$offer['id']]);
                    $offer_clicks = $stmt->fetch()['count'];
                    
                    // عدد النقرات من هذا الـ IP
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM click_tracking WHERE offer_id = ? AND ip_address = ?");
                    $stmt->execute([$offer['id'], $user_ip]);
                    $ip_clicks = $stmt->fetch()['count'];
                    
                    echo "<p><strong>إجمالي النقرات لهذا العرض:</strong> $offer_clicks</p>";
                    echo "<p><strong>نقرات من IP الحالي:</strong> $ip_clicks</p>";
                    
                    if ($ip_clicks > 0) {
                        $stmt = $pdo->prepare("SELECT click_date FROM click_tracking WHERE offer_id = ? AND ip_address = ? ORDER BY click_date DESC LIMIT 1");
                        $stmt->execute([$offer['id'], $user_ip]);
                        $last_click = $stmt->fetch();
                        if ($last_click) {
                            echo "<p><strong>آخر نقرة:</strong> " . date('Y-m-d H:i:s', strtotime($last_click['click_date'])) . "</p>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<p>❌ خطأ في جلب الإحصائيات: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                ?>
            </div>
        </div>
    </body>
    </html>
    <?php
} else {
    // التحويل الفعلي
    echo "<h1>🔄 جاري التحويل...</h1>";
    echo "<p>سيتم تحويلك خلال 3 ثوان...</p>";
    echo "<script>";
    echo "setTimeout(function() {";
    echo "window.location.href = 'click_handler.php?offer_id=$offer_id';";
    echo "}, 3000);";
    echo "</script>";
    echo "<p><a href='click_handler.php?offer_id=$offer_id'>انقر هنا إذا لم يتم التحويل تلقائياً</a></p>";
}
?>
