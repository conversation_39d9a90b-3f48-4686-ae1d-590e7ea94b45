<?php
// ملف اختبار صفحة فحص IP
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 اختبار صفحة فحص IP</h1>";

// فحص الملفات المطلوبة
echo "<h2>1️⃣ فحص الملفات المطلوبة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

$required_files = ['config.php', 'site_protection.php', 'ip_check.php'];
foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

echo "</div>";

// فحص الدوال المطلوبة
echo "<h2>2️⃣ فحص الدوال:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";

// تضمين الملفات
try {
    require_once 'config.php';
    echo "✅ تم تحميل config.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل config.php: " . $e->getMessage() . "<br>";
}

// فحص الدوال
$functions_to_check = [
    'getUserIP' => 'دالة الحصول على IP المستخدم',
    'file_get_contents' => 'دالة جلب محتوى URL',
    'json_decode' => 'دالة فك تشفير JSON',
    'stream_context_create' => 'دالة إنشاء سياق HTTP'
];

foreach ($functions_to_check as $function => $description) {
    if (function_exists($function)) {
        echo "✅ $description ($function)<br>";
    } else {
        echo "❌ $description ($function) غير متوفرة<br>";
    }
}

echo "</div>";

// اختبار API
echo "<h2>3️⃣ اختبار IP Quality Score API:</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";

$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$test_ip = '*******'; // Google DNS للاختبار

echo "<strong>🔑 API Key:</strong> " . substr($api_key, 0, 10) . "..." . substr($api_key, -5) . "<br>";
echo "<strong>🌐 IP للاختبار:</strong> $test_ip<br><br>";

$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$test_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";

echo "<strong>📡 رابط API:</strong><br>";
echo "<code style='background: #f8f9fa; padding: 5px; border-radius: 3px; font-size: 12px; word-break: break-all;'>";
echo htmlspecialchars($api_url);
echo "</code><br><br>";

// فحص إعدادات PHP
echo "<strong>⚙️ إعدادات PHP:</strong><br>";
echo "- allow_url_fopen: " . (ini_get('allow_url_fopen') ? '✅ مفعل' : '❌ معطل') . "<br>";
echo "- user_agent: " . (ini_get('user_agent') ?: 'غير محدد') . "<br>";
echo "- default_socket_timeout: " . ini_get('default_socket_timeout') . " ثانية<br><br>";

// اختبار الاتصال
echo "<strong>🔗 اختبار الاتصال:</strong><br>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$start_time = microtime(true);
$response = @file_get_contents($api_url, false, $context);
$end_time = microtime(true);
$response_time = round(($end_time - $start_time) * 1000, 2);

if ($response !== false) {
    echo "✅ تم الاتصال بنجاح<br>";
    echo "⏱️ وقت الاستجابة: {$response_time}ms<br>";
    
    $ip_data = json_decode($response, true);
    if ($ip_data) {
        echo "✅ تم فك تشفير JSON بنجاح<br>";
        
        if (isset($ip_data['success']) && $ip_data['success']) {
            echo "✅ API يعمل بشكل صحيح<br>";
            echo "<strong>📊 نتائج الاختبار:</strong><br>";
            echo "- البلد: " . ($ip_data['country_code'] ?? 'غير محدد') . "<br>";
            echo "- المدينة: " . ($ip_data['city'] ?? 'غير محدد') . "<br>";
            echo "- مزود الخدمة: " . ($ip_data['ISP'] ?? 'غير محدد') . "<br>";
            echo "- نقاط الاحتيال: " . ($ip_data['fraud_score'] ?? 0) . "<br>";
            echo "- بروكسي: " . (($ip_data['proxy'] ?? false) ? 'نعم' : 'لا') . "<br>";
        } else {
            echo "❌ API أرجع خطأ: " . ($ip_data['message'] ?? 'خطأ غير محدد') . "<br>";
        }
    } else {
        echo "❌ فشل في فك تشفير JSON<br>";
        echo "<strong>الاستجابة الخام:</strong><br>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
        echo htmlspecialchars(substr($response, 0, 500));
        echo "</pre>";
    }
} else {
    echo "❌ فشل في الاتصال<br>";
    
    // فحص الأخطاء
    $error = error_get_last();
    if ($error) {
        echo "<strong>تفاصيل الخطأ:</strong><br>";
        echo "- النوع: " . $error['type'] . "<br>";
        echo "- الرسالة: " . htmlspecialchars($error['message']) . "<br>";
    }
}

echo "</div>";

// فحص ملف سجل API
echo "<h2>4️⃣ فحص سجل استهلاك API:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";

$log_file = 'api_usage.txt';
if (file_exists($log_file)) {
    echo "✅ ملف السجل موجود<br>";
    
    $file_size = filesize($log_file);
    echo "📁 حجم الملف: " . number_format($file_size) . " بايت<br>";
    
    if (is_readable($log_file)) {
        echo "✅ الملف قابل للقراءة<br>";
        
        $logs = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $total_requests = count($logs);
        echo "📊 إجمالي الطلبات: $total_requests<br>";
        
        if ($total_requests > 0) {
            $today = date('Y-m-d');
            $today_count = 0;
            $recent_logs = array_slice($logs, -10); // آخر 10 طلبات
            
            foreach ($logs as $log) {
                if (strpos($log, $today) === 0) {
                    $today_count++;
                }
            }
            
            echo "📅 طلبات اليوم: $today_count<br>";
            echo "<strong>📋 آخر 10 طلبات:</strong><br>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
            foreach ($recent_logs as $log) {
                echo htmlspecialchars($log) . "\n";
            }
            echo "</pre>";
        }
    } else {
        echo "❌ الملف غير قابل للقراءة<br>";
    }
} else {
    echo "⚠️ ملف السجل غير موجود (سيتم إنشاؤه عند أول استخدام)<br>";
}

if (is_writable('.')) {
    echo "✅ المجلد قابل للكتابة<br>";
} else {
    echo "❌ المجلد غير قابل للكتابة<br>";
}

echo "</div>";

// اختبار الدوال المحلية
echo "<h2>5️⃣ اختبار الدوال المحلية:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460;'>";

// اختبار getUserIP
if (function_exists('getUserIP')) {
    $user_ip = getUserIP();
    echo "✅ getUserIP(): $user_ip<br>";
} else {
    echo "❌ دالة getUserIP غير موجودة<br>";
}

// اختبار دوال أخرى من ip_check.php
$test_functions = [
    'getRiskLevel' => [85, 'اختبار مستوى خطر عالي'],
    'getBooleanText' => [true, 'اختبار تحويل منطقي'],
];

foreach ($test_functions as $func_name => $test_data) {
    if (function_exists($func_name)) {
        try {
            $result = call_user_func($func_name, $test_data[0]);
            echo "✅ $func_name(): " . json_encode($result) . " - {$test_data[1]}<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في $func_name(): " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠️ دالة $func_name غير موجودة (ربما محلية في ip_check.php)<br>";
    }
}

echo "</div>";

// نصائح لحل المشاكل
echo "<h2>6️⃣ نصائح لحل المشاكل الشائعة:</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";

echo "<strong>🔧 إذا كانت الصفحة لا تعمل:</strong><br>";
echo "1. تأكد من أن allow_url_fopen مفعل في PHP<br>";
echo "2. فحص صحة API Key<br>";
echo "3. التأكد من الاتصال بالإنترنت<br>";
echo "4. فحص صلاحيات الكتابة للمجلد<br>";
echo "5. مراجعة سجل أخطاء الخادم<br><br>";

echo "<strong>⚠️ إذا كان API لا يعمل:</strong><br>";
echo "1. فحص حالة خدمة IP Quality Score<br>";
echo "2. التأكد من عدم تجاوز الحد الشهري (5000 طلب)<br>";
echo "3. فحص تنسيق API Key<br>";
echo "4. اختبار IP مختلف<br><br>";

echo "<strong>📊 إذا كانت الإحصائيات خاطئة:</strong><br>";
echo "1. فحص ملف api_usage.txt<br>";
echo "2. التأكد من صلاحيات الكتابة<br>";
echo "3. حذف الملف لإعادة تعيين العداد<br>";
echo "4. فحص تنسيق التواريخ في السجل<br>";

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='ip_check.php' style='color: #007bff;'>🔍 صفحة فحص IP</a> - الصفحة الأصلية</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - للإحصائيات العامة</p>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - العودة للرئيسية</p>";

// معلومات إضافية
echo "<hr>";
echo "<h2>ℹ️ معلومات النظام:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; font-size: 14px;'>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "<br>";
echo "<strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد') . "<br>";
echo "<strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "<strong>Memory Limit:</strong> " . ini_get('memory_limit') . "<br>";
echo "<strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}
</style>
