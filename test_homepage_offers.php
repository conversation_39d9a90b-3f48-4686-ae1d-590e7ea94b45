<?php
// ملف اختبار عرض العروض في الصفحة الرئيسية
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config.php';

echo "<h1>🏠 اختبار عرض العروض في الصفحة الرئيسية</h1>";

// محاكاة الحصول على IP المستخدم
$user_ip = getUserIP();
echo "<h2>1️⃣ معلومات المستخدم:</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";
echo "<strong>🌐 IP المستخدم:</strong> $user_ip<br>";

// محاكاة الحصول على بلد المستخدم
$user_country = null;
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);
if ($response !== false) {
    $ip_data = json_decode($response, true);
    if ($ip_data && isset($ip_data['success']) && $ip_data['success']) {
        $user_country = $ip_data['country_code'] ?? null;
        echo "<strong>🏳️ بلد المستخدم:</strong> $user_country (" . getCountryName($user_country) . ")<br>";
    } else {
        echo "<strong>⚠️ بلد المستخدم:</strong> غير محدد (فشل في API)<br>";
    }
} else {
    echo "<strong>❌ بلد المستخدم:</strong> غير محدد (فشل في الاتصال)<br>";
}
echo "</div>";

echo "<h2>2️⃣ جلب العروض حسب البلد:</h2>";

// جلب العروض حسب بلد المستخدم
$offers = getOffersByCountry($pdo, $user_country);
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";
echo "<strong>📊 إحصائيات العروض:</strong><br>";
echo "- عدد العروض المجلبة: " . count($offers) . "<br>";
echo "- البحث للبلد: " . ($user_country ?: 'جميع البلدان') . "<br>";
echo "</div>";

if (!empty($offers)) {
    echo "<h3>📋 قائمة العروض المجلبة:</h3>";
    foreach ($offers as $index => $offer) {
        echo "<div style='border: 1px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
        echo "<h4>🎯 العرض " . ($index + 1) . ": " . htmlspecialchars($offer['name']) . "</h4>";
        echo "<strong>🆔 ID:</strong> " . $offer['id'] . "<br>";
        echo "<strong>🔗 الرابط:</strong> <a href='" . htmlspecialchars($offer['link']) . "' target='_blank'>" . htmlspecialchars($offer['link']) . "</a><br>";
        echo "<strong>🖼️ الصورة:</strong> <img src='" . htmlspecialchars($offer['image']) . "' alt='صورة العرض' style='max-width: 100px; height: auto; border-radius: 5px;'><br>";
        echo "<strong>📝 الوصف:</strong> " . htmlspecialchars($offer['description'] ?: 'غير متوفر') . "<br>";
        echo "<strong>⚠️ الشروط:</strong> " . htmlspecialchars($offer['additional_terms'] ?: 'غير متوفر') . "<br>";
        echo "<strong>🌍 البلدان المستهدفة:</strong> <span style='color: #28a745; font-weight: bold;'>" . htmlspecialchars($offer['target_countries'] ?: 'غير محدد') . "</span><br>";
        echo "<strong>📅 تاريخ الإضافة:</strong> " . ($offer['created_date'] ?? 'غير محدد') . "<br>";
        
        // تحليل البلدان المستهدفة
        echo "<br><strong>🔍 تحليل البلدان:</strong><br>";
        $target_countries = $offer['target_countries'];
        if (empty($target_countries) || $target_countries === 'ALL') {
            echo "- هذا العرض متاح لجميع البلدان 🌐<br>";
        } else {
            $countries_array = array_map('trim', explode(',', $target_countries));
            echo "- البلدان المستهدفة: " . implode(', ', $countries_array) . "<br>";
            
            if ($user_country) {
                $is_targeted = in_array($user_country, $countries_array) || 
                              strpos($target_countries, $user_country) !== false;
                $status = $is_targeted ? '✅ مستهدف' : '❌ غير مستهدف';
                echo "- حالة بلدك ($user_country): $status<br>";
            }
        }
        echo "</div>";
    }
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; color: #856404;'>";
    echo "<strong>⚠️ لا توجد عروض متاحة</strong><br>";
    echo "الأسباب المحتملة:<br>";
    echo "- لا توجد عروض مضافة في قاعدة البيانات<br>";
    echo "- لا توجد عروض تستهدف بلدك ($user_country)<br>";
    echo "- مشكلة في دالة getOffersByCountry<br>";
    echo "</div>";
}

echo "<h2>3️⃣ اختبار نظام منع التكرار:</h2>";

$anti_duplicate_settings = getAntiDuplicateSettings($pdo);
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";
echo "<strong>⚙️ إعدادات منع التكرار:</strong><br>";
echo "- مفعل: " . ($anti_duplicate_settings['is_enabled'] ? '✅ نعم' : '❌ لا') . "<br>";
echo "- مدة المنع: " . $anti_duplicate_settings['block_duration_days'] . " يوم<br>";
echo "- نوع المنع: " . $anti_duplicate_settings['block_type'] . "<br>";
echo "- IPs مستثناة: " . ($anti_duplicate_settings['whitelist_ips'] ?: 'لا يوجد') . "<br>";
echo "</div>";

if ($anti_duplicate_settings['is_enabled'] && !empty($offers)) {
    echo "<h3>🛡️ فلترة العروض حسب نظام منع التكرار:</h3>";
    $filtered_offers = [];
    
    foreach ($offers as $offer) {
        $is_blocked = isIPBlocked($pdo, $offer['id'], $user_ip);
        $status = $is_blocked ? '🚫 محجوب' : '✅ متاح';
        echo "<div style='padding: 10px; margin: 5px 0; border-radius: 5px; " . 
             ($is_blocked ? "background: #f8d7da; color: #721c24;" : "background: #d4edda; color: #155724;") . "'>";
        echo "<strong>العرض:</strong> " . htmlspecialchars($offer['name']) . " - <strong>الحالة:</strong> $status<br>";
        
        if (!$is_blocked) {
            $filtered_offers[] = $offer;
        }
        echo "</div>";
    }
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; color: #0c5460; margin-top: 15px;'>";
    echo "<strong>📊 نتائج الفلترة:</strong><br>";
    echo "- العروض الأصلية: " . count($offers) . "<br>";
    echo "- العروض بعد الفلترة: " . count($filtered_offers) . "<br>";
    echo "- العروض المحجوبة: " . (count($offers) - count($filtered_offers)) . "<br>";
    echo "</div>";
    
    $offers = $filtered_offers; // تحديث المتغير
}

echo "<h2>4️⃣ محاكاة عرض العروض في الصفحة الرئيسية:</h2>";

if (!empty($offers)) {
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    foreach ($offers as $offer) {
        echo "<div style='background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: transform 0.3s ease;'>";
        
        // شارة البلدان المستهدفة
        if (!empty($offer['target_countries'])) {
            echo "<div style='background: #007bff; color: white; padding: 5px 10px; font-size: 12px; text-align: center;'>";
            if ($offer['target_countries'] === 'ALL') {
                echo '<span title="متاح لجميع البلدان" style="cursor: help;">🌐 جميع البلدان</span>';
            } else {
                $countries = explode(',', $offer['target_countries']);
                $countries = array_map('trim', $countries);
                $displayed_countries = array_slice($countries, 0, 3);
                $country_items = [];

                foreach ($displayed_countries as $country) {
                    $country_name = getCountryName($country);
                    $country_items[] = '<span title="' . htmlspecialchars($country_name) . '" style="cursor: help; border-bottom: 1px dotted rgba(255,255,255,0.7);">' . htmlspecialchars($country) . '</span>';
                }

                echo implode(', ', $country_items);

                if (count($countries) > 3) {
                    $remaining_countries = array_slice($countries, 3);
                    $remaining_names = array_map('getCountryName', $remaining_countries);
                    $tooltip_text = implode('، ', $remaining_names);
                    echo ' <span title="' . htmlspecialchars($tooltip_text) . '" style="cursor: help; border-bottom: 1px dotted rgba(255,255,255,0.7);">+' . (count($countries) - 3) . ' أخرى</span>';
                }
            }
            echo "</div>";
        }
        
        // صورة العرض
        echo "<img src='" . htmlspecialchars($offer['image']) . "' alt='" . htmlspecialchars($offer['name']) . "' style='width: 100%; height: 180px; object-fit: cover;' onerror=\"this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPtit2YXZhNin2YQg2KfZhNi12YjYsdipPC90ZXh0Pjwvc3ZnPg=='\">";
        
        // محتوى العرض
        echo "<div style='padding: 20px;'>";
        echo "<div style='font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px;'>" . htmlspecialchars($offer['name']) . "</div>";
        
        if (!empty($offer['description'])) {
            echo "<div style='color: #666; margin-bottom: 10px;'>";
            echo "<strong>📝 الوصف:</strong><br>";
            echo htmlspecialchars(substr($offer['description'], 0, 120));
            if (strlen($offer['description']) > 120) echo '...';
            echo "</div>";
        }
        
        if (!empty($offer['additional_terms'])) {
            echo "<div style='color: #666; margin-bottom: 15px;'>";
            echo "<strong>⚠️ شروط إضافية:</strong><br>";
            echo htmlspecialchars(substr($offer['additional_terms'], 0, 100));
            if (strlen($offer['additional_terms']) > 100) echo '...';
            echo "</div>";
        }
        
        echo "<a href='click_handler.php?offer_id=" . $offer['id'] . "' target='_blank' style='display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; font-weight: 600; transition: all 0.3s ease;'>🔗 مشاهدة العرض</a>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; text-align: center;'>";
    echo "<h3>😔 لا توجد عروض متاحة حالياً</h3>";
    echo "<p>يرجى المحاولة لاحقاً أو التحقق من إعدادات العروض</p>";
    echo "</div>";
}

echo "<h2>5️⃣ إحصائيات شاملة:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

// إجمالي العروض في قاعدة البيانات
$total_offers = getAllOffers($pdo);
echo "<div style='background: #007bff; color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>" . count($total_offers) . "</div>";
echo "<div>إجمالي العروض</div>";
echo "</div>";

// العروض المتاحة للمستخدم
echo "<div style='background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>" . count($offers) . "</div>";
echo "<div>العروض المتاحة لك</div>";
echo "</div>";

// العروض المحجوبة
$blocked_count = count(getAllOffers($pdo)) - count($offers);
echo "<div style='background: #dc3545; color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$blocked_count</div>";
echo "<div>العروض المحجوبة</div>";
echo "</div>";

// بلد المستخدم
echo "<div style='background: #6f42c1; color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 16px; font-weight: bold;'>" . ($user_country ?: 'غير محدد') . "</div>";
echo "<div>بلدك</div>";
echo "</div>";

echo "</div>";

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - مشاهدة الصفحة الأصلية</p>";
echo "<p><a href='api_offers.php' style='color: #007bff;'>🌐 عروض API</a> - إضافة عروض جديدة</p>";
echo "<p><a href='manage_offers.php' style='color: #007bff;'>🗂️ إدارة العروض</a> - تعديل العروض الموجودة</p>";
echo "<p><a href='test_offers_conversion.php' style='color: #007bff;'>🔄 اختبار تحويل العروض</a> - فحص معالجة البيانات</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

img {
    max-width: 100%;
    height: auto;
}
</style>
