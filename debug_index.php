<?php
// ملف تشخيص مشاكل الصفحة الرئيسية
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص مشاكل الصفحة الرئيسية</h1>";

try {
    echo "<h2>1️⃣ فحص الملفات المطلوبة:</h2>";
    
    if (file_exists('config.php')) {
        echo "✅ config.php موجود<br>";
        require_once 'config.php';
        echo "✅ تم تحميل config.php بنجاح<br>";
    } else {
        echo "❌ config.php غير موجود<br>";
        exit;
    }
    
    if (file_exists('site_protection.php')) {
        echo "✅ site_protection.php موجود<br>";
        require_once 'site_protection.php';
        echo "✅ تم تحميل site_protection.php بنجاح<br>";
    } else {
        echo "❌ site_protection.php غير موجود<br>";
    }
    
    echo "<h2>2️⃣ فحص الاتصال بقاعدة البيانات:</h2>";
    
    if (isset($pdo)) {
        echo "✅ متغير PDO موجود<br>";
        
        // اختبار الاتصال
        $stmt = $pdo->query("SELECT 1");
        if ($stmt) {
            echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
        } else {
            echo "❌ فشل في الاتصال بقاعدة البيانات<br>";
        }
    } else {
        echo "❌ متغير PDO غير موجود<br>";
    }
    
    echo "<h2>3️⃣ فحص الدوال المطلوبة:</h2>";
    
    $required_functions = [
        'getUserIP',
        'getOffersByCountry',
        'getAntiDuplicateSettings',
        'isIPBlocked',
        'getCountryName'
    ];
    
    foreach ($required_functions as $function) {
        if (function_exists($function)) {
            echo "✅ الدالة $function موجودة<br>";
        } else {
            echo "❌ الدالة $function غير موجودة<br>";
        }
    }
    
    echo "<h2>4️⃣ فحص IP المستخدم:</h2>";
    
    if (function_exists('getUserIP')) {
        $user_ip = getUserIP();
        echo "✅ IP المستخدم: $user_ip<br>";
    } else {
        echo "❌ لا يمكن الحصول على IP المستخدم<br>";
    }
    
    echo "<h2>5️⃣ فحص بيانات البلد:</h2>";
    
    $api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
    $api_url = "https://ipqualityscore.com/api/json/ip/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $response = @file_get_contents($api_url, false, $context);
    $user_country = null;
    
    if ($response !== false) {
        $ip_data = json_decode($response, true);
        if ($ip_data && isset($ip_data['success']) && $ip_data['success']) {
            $user_country = $ip_data['country_code'] ?? null;
            echo "✅ بلد المستخدم: $user_country<br>";
            if (function_exists('getCountryName')) {
                echo "✅ اسم البلد: " . getCountryName($user_country) . "<br>";
            }
        } else {
            echo "❌ فشل في الحصول على بيانات البلد من API<br>";
            echo "📋 استجابة API: " . htmlspecialchars($response) . "<br>";
        }
    } else {
        echo "❌ فشل في الاتصال بـ API<br>";
    }
    
    echo "<h2>6️⃣ فحص العروض:</h2>";
    
    if (function_exists('getOffersByCountry')) {
        $offers = getOffersByCountry($pdo, $user_country);
        echo "✅ عدد العروض المجلبة: " . count($offers) . "<br>";
        
        if (!empty($offers)) {
            echo "<h3>📋 قائمة العروض:</h3>";
            foreach ($offers as $index => $offer) {
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
                echo "<strong>العرض " . ($index + 1) . ":</strong> " . htmlspecialchars($offer['name']) . "<br>";
                echo "<strong>ID:</strong> " . $offer['id'] . "<br>";
                echo "<strong>البلدان المستهدفة:</strong> " . ($offer['target_countries'] ?: 'غير محدد') . "<br>";
                echo "</div>";
            }
        } else {
            echo "⚠️ لا توجد عروض متاحة<br>";
        }
    } else {
        echo "❌ دالة getOffersByCountry غير موجودة<br>";
    }
    
    echo "<h2>7️⃣ فحص نظام منع التكرار:</h2>";
    
    if (function_exists('getAntiDuplicateSettings')) {
        $anti_duplicate_settings = getAntiDuplicateSettings($pdo);
        echo "✅ إعدادات منع التكرار:<br>";
        echo "- مفعل: " . ($anti_duplicate_settings['is_enabled'] ? 'نعم' : 'لا') . "<br>";
        echo "- مدة المنع: " . $anti_duplicate_settings['block_duration_days'] . " يوم<br>";
        echo "- نوع المنع: " . $anti_duplicate_settings['block_type'] . "<br>";
        
        if (!empty($offers) && function_exists('isIPBlocked')) {
            $filtered_offers = [];
            foreach ($offers as $offer) {
                $is_blocked = isIPBlocked($pdo, $offer['id'], $user_ip);
                echo "- العرض " . $offer['id'] . ": " . ($is_blocked ? 'محجوب' : 'متاح') . "<br>";
                if (!$is_blocked) {
                    $filtered_offers[] = $offer;
                }
            }
            echo "✅ عدد العروض بعد الفلترة: " . count($filtered_offers) . "<br>";
        }
    } else {
        echo "❌ دالة getAntiDuplicateSettings غير موجودة<br>";
    }
    
    echo "<h2>8️⃣ فحص نظام الحماية:</h2>";
    
    if (function_exists('getSiteProtectionSettings')) {
        $protection_settings = getSiteProtectionSettings($pdo);
        echo "✅ إعدادات الحماية:<br>";
        echo "- مفعل: " . ($protection_settings['is_locked'] ? 'نعم' : 'لا') . "<br>";
        echo "- كلمة المرور: " . (!empty($protection_settings['password_hash']) ? 'محددة' : 'غير محددة') . "<br>";
        echo "- رسالة الحماية: " . htmlspecialchars($protection_settings['lock_message']) . "<br>";
        echo "- IPs مسموحة: " . ($protection_settings['allowed_ips'] ?: 'لا يوجد') . "<br>";
        
        if (function_exists('isIPAllowed')) {
            $is_ip_allowed = isIPAllowed($pdo, $user_ip);
            echo "- IP الحالي مسموح: " . ($is_ip_allowed ? 'نعم' : 'لا') . "<br>";
        }
    } else {
        echo "❌ دالة getSiteProtectionSettings غير موجودة<br>";
    }
    
    echo "<h2>9️⃣ فحص الجلسة:</h2>";
    
    session_start();
    echo "✅ الجلسة مبدوءة<br>";
    echo "- ID الجلسة: " . session_id() . "<br>";
    echo "- فتح القفل: " . (isset($_SESSION['site_unlocked']) && $_SESSION['site_unlocked'] ? 'نعم' : 'لا') . "<br>";
    
    echo "<h2>🔟 فحص الأخطاء:</h2>";
    
    $error_log = error_get_last();
    if ($error_log) {
        echo "❌ آخر خطأ:<br>";
        echo "- النوع: " . $error_log['type'] . "<br>";
        echo "- الرسالة: " . htmlspecialchars($error_log['message']) . "<br>";
        echo "- الملف: " . $error_log['file'] . "<br>";
        echo "- السطر: " . $error_log['line'] . "<br>";
    } else {
        echo "✅ لا توجد أخطاء مسجلة<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<strong>❌ خطأ:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>📁 الملف:</strong> " . $e->getFile() . "<br>";
    echo "<strong>📍 السطر:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a> - اختبار الصفحة الأصلية</p>";
echo "<p><a href='admin.php' style='color: #007bff;'>⚙️ لوحة التحكم</a> - إدارة الموقع</p>";
echo "<p><a href='test_site_protection.php' style='color: #007bff;'>🔑 اختبار الحماية</a> - فحص نظام الحماية</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

div {
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
