<?php
// إعدادات قاعدة البيانات
$db_host = 'sql303.infinityfree.com';
$db_name = 'if0_39395085_q12';
$db_user = 'if0_39395085';
$db_pass = 'Qweeee12';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // إنشاء جدول المستخدمين إذا لم يكن موجوداً
    $createUsersTable = "
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createUsersTable);
    
    // إنشاء جدول العروض إذا لم يكن موجوداً
    $createOffersTable = "
        CREATE TABLE IF NOT EXISTS offers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            link TEXT NOT NULL,
            image TEXT NOT NULL,
            description TEXT,
            additional_terms TEXT,
            target_countries TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createOffersTable);

    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    try {
        $pdo->exec("ALTER TABLE offers ADD COLUMN description TEXT");
    } catch(PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        $pdo->exec("ALTER TABLE offers ADD COLUMN additional_terms TEXT");
    } catch(PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        $pdo->exec("ALTER TABLE offers ADD COLUMN target_countries TEXT");
    } catch(PDOException $e) {
        // العمود موجود بالفعل
    }

    // إنشاء جدول أسماء المستخدمين العشوائية
    $createRandomUsersTable = "
        CREATE TABLE IF NOT EXISTS random_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL UNIQUE,
            username VARCHAR(50) NOT NULL UNIQUE,
            subid VARCHAR(100) NOT NULL UNIQUE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_ip (ip_address),
            INDEX idx_subid (subid)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createRandomUsersTable);

    // إنشاء جدول التحويلات إذا لم يكن موجوداً
    $createConversionsTable = "
        CREATE TABLE IF NOT EXISTS conversions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            campaign_id VARCHAR(100),
            campaign_name VARCHAR(255),
            subid VARCHAR(100),
            subid2 VARCHAR(100),
            subid3 VARCHAR(100),
            idfa VARCHAR(100),
            gaid VARCHAR(100),
            payout DECIMAL(10,2),
            ip_address VARCHAR(45),
            gateway_id VARCHAR(100),
            lead_id VARCHAR(100),
            country_iso VARCHAR(2),
            virtual_currency INT DEFAULT 0,
            conversion_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('pending', 'confirmed', 'rejected') DEFAULT 'pending'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createConversionsTable);

    // إنشاء جدول إعدادات Postback إذا لم يكن موجوداً
    $createPostbackSettingsTable = "
        CREATE TABLE IF NOT EXISTS postback_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            postback_url TEXT,
            postback_password VARCHAR(255),
            is_enabled BOOLEAN DEFAULT FALSE,
            whitelist_ip VARCHAR(45) DEFAULT '************',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createPostbackSettingsTable);
    
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دالة لتنظيف البيانات
function cleanInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة لإدراج مستخدم جديد
function insertUser($pdo, $firstName, $lastName) {
    try {
        $stmt = $pdo->prepare("INSERT INTO users (first_name, last_name) VALUES (?, ?)");
        return $stmt->execute([$firstName, $lastName]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب جميع المستخدمين
function getAllUsers($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM users ORDER BY registration_date DESC");
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لإدراج عرض جديد
function insertOffer($pdo, $name, $link, $image, $description = '', $additional_terms = '', $target_countries = '') {
    try {
        $stmt = $pdo->prepare("INSERT INTO offers (name, link, image, description, additional_terms, target_countries) VALUES (?, ?, ?, ?, ?, ?)");
        return $stmt->execute([$name, $link, $image, $description, $additional_terms, $target_countries]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب جميع العروض
function getAllOffers($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM offers ORDER BY created_date DESC");
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لحذف عرض
function deleteOffer($pdo, $offerId) {
    try {
        $stmt = $pdo->prepare("DELETE FROM offers WHERE id = ?");
        return $stmt->execute([$offerId]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لعد المستخدمين
function countUsers($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        return $result['count'];
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لعد العروض
function countOffers($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM offers");
        $result = $stmt->fetch();
        return $result['count'];
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لإدراج تحويل جديد
function insertConversion($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO conversions
            (campaign_id, campaign_name, subid, subid2, subid3, idfa, gaid, payout, ip_address, gateway_id, lead_id, country_iso, virtual_currency)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        return $stmt->execute([
            $data['campaign_id'] ?? '',
            $data['campaign_name'] ?? '',
            $data['subid'] ?? '',
            $data['subid2'] ?? '',
            $data['subid3'] ?? '',
            $data['idfa'] ?? '',
            $data['gaid'] ?? '',
            $data['payout'] ?? 0,
            $data['ip_address'] ?? '',
            $data['gateway_id'] ?? '',
            $data['lead_id'] ?? '',
            $data['country_iso'] ?? '',
            $data['virtual_currency'] ?? 0
        ]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب جميع التحويلات
function getAllConversions($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM conversions ORDER BY conversion_date DESC");
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لعد التحويلات
function countConversions($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM conversions");
        $result = $stmt->fetch();
        return $result['count'];
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لحفظ إعدادات Postback
function savePostbackSettings($pdo, $url, $password, $enabled) {
    try {
        // حذف الإعدادات القديمة
        $pdo->exec("DELETE FROM postback_settings");

        // إدراج الإعدادات الجديدة
        $stmt = $pdo->prepare("INSERT INTO postback_settings (postback_url, postback_password, is_enabled) VALUES (?, ?, ?)");
        return $stmt->execute([$url, $password, $enabled]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب إعدادات Postback
function getPostbackSettings($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM postback_settings ORDER BY id DESC LIMIT 1");
        return $stmt->fetch();
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لحساب إجمالي الأرباح
function getTotalEarnings($pdo) {
    try {
        $stmt = $pdo->query("SELECT SUM(payout) as total FROM conversions WHERE status = 'confirmed'");
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لجلب العروض حسب البلد
function getOffersByCountry($pdo, $country_code = '') {
    try {
        if (empty($country_code)) {
            // إذا لم يتم تحديد بلد، اجلب جميع العروض
            $stmt = $pdo->query("SELECT * FROM offers ORDER BY created_date DESC");
            return $stmt->fetchAll();
        } else {
            // جلب العروض التي تستهدف البلد المحدد أو العروض العامة
            $stmt = $pdo->prepare("
                SELECT * FROM offers
                WHERE target_countries IS NULL
                   OR target_countries = ''
                   OR target_countries LIKE ?
                   OR target_countries LIKE ?
                ORDER BY created_date DESC
            ");
            $country_pattern1 = "%$country_code%";
            $country_pattern2 = "%ALL%";
            $stmt->execute([$country_pattern1, $country_pattern2]);
            return $stmt->fetchAll();
        }
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لتحويل رمز البلد إلى اسم البلد
function getCountryName($country_code) {
    $countries = [
        // أمريكا الشمالية
        'US' => 'الولايات المتحدة',
        'CA' => 'كندا',
        'MX' => 'المكسيك',

        // أوروبا
        'GB' => 'المملكة المتحدة',
        'DE' => 'ألمانيا',
        'FR' => 'فرنسا',
        'IT' => 'إيطاليا',
        'ES' => 'إسبانيا',
        'NL' => 'هولندا',
        'BE' => 'بلجيكا',
        'CH' => 'سويسرا',
        'AT' => 'النمسا',
        'SE' => 'السويد',
        'NO' => 'النرويج',
        'DK' => 'الدنمارك',
        'FI' => 'فنلندا',
        'IE' => 'أيرلندا',
        'PT' => 'البرتغال',
        'GR' => 'اليونان',
        'PL' => 'بولندا',
        'CZ' => 'التشيك',
        'HU' => 'المجر',
        'RO' => 'رومانيا',
        'BG' => 'بلغاريا',
        'HR' => 'كرواتيا',
        'SI' => 'سلوفينيا',
        'SK' => 'سلوفاكيا',
        'EE' => 'إستونيا',
        'LV' => 'لاتفيا',
        'LT' => 'ليتوانيا',

        // آسيا والمحيط الهادئ
        'AU' => 'أستراليا',
        'NZ' => 'نيوزيلندا',
        'JP' => 'اليابان',
        'KR' => 'كوريا الجنوبية',
        'SG' => 'سنغافورة',
        'HK' => 'هونغ كونغ',
        'TW' => 'تايوان',
        'MY' => 'ماليزيا',
        'TH' => 'تايلاند',
        'PH' => 'الفلبين',
        'ID' => 'إندونيسيا',
        'VN' => 'فيتنام',
        'IN' => 'الهند',
        'CN' => 'الصين',

        // الشرق الأوسط وأفريقيا
        'AE' => 'الإمارات العربية المتحدة',
        'SA' => 'السعودية',
        'QA' => 'قطر',
        'KW' => 'الكويت',
        'BH' => 'البحرين',
        'OM' => 'عمان',
        'JO' => 'الأردن',
        'LB' => 'لبنان',
        'EG' => 'مصر',
        'MA' => 'المغرب',
        'TN' => 'تونس',
        'DZ' => 'الجزائر',
        'IL' => 'إسرائيل',
        'TR' => 'تركيا',
        'ZA' => 'جنوب أفريقيا',
        'NG' => 'نيجيريا',
        'KE' => 'كينيا',
        'GH' => 'غانا',

        // أمريكا الجنوبية
        'BR' => 'البرازيل',
        'AR' => 'الأرجنتين',
        'CL' => 'تشيلي',
        'CO' => 'كولومبيا',
        'PE' => 'بيرو',
        'VE' => 'فنزويلا',
        'UY' => 'أوروغواي',
        'EC' => 'الإكوادور',

        // خاص
        'ALL' => 'جميع البلدان'
    ];

    return $countries[$country_code] ?? $country_code;
}
?>
