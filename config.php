<?php
// إعدادات قاعدة البيانات
$db_host = 'sql303.infinityfree.com';
$db_name = 'if0_39395085_q12';
$db_user = 'if0_39395085';
$db_pass = 'Qweeee12';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // إنشاء جدول المستخدمين إذا لم يكن موجوداً
    $createUsersTable = "
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createUsersTable);
    
    // إنشاء جدول العروض إذا لم يكن موجوداً
    $createOffersTable = "
        CREATE TABLE IF NOT EXISTS offers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            link TEXT NOT NULL,
            image TEXT NOT NULL,
            description TEXT,
            additional_terms TEXT,
            target_countries TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createOffersTable);

    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    try {
        $pdo->exec("ALTER TABLE offers ADD COLUMN description TEXT");
    } catch(PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        $pdo->exec("ALTER TABLE offers ADD COLUMN additional_terms TEXT");
    } catch(PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        $pdo->exec("ALTER TABLE offers ADD COLUMN target_countries TEXT");
    } catch(PDOException $e) {
        // العمود موجود بالفعل
    }

    // إنشاء جدول أسماء المستخدمين العشوائية
    $createRandomUsersTable = "
        CREATE TABLE IF NOT EXISTS random_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL UNIQUE,
            username VARCHAR(50) NOT NULL UNIQUE,
            subid VARCHAR(100) NOT NULL UNIQUE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_ip (ip_address),
            INDEX idx_subid (subid)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createRandomUsersTable);

    // إنشاء جدول التحويلات إذا لم يكن موجوداً
    $createConversionsTable = "
        CREATE TABLE IF NOT EXISTS conversions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            campaign_id VARCHAR(100),
            campaign_name VARCHAR(255),
            subid VARCHAR(100),
            subid2 VARCHAR(100),
            subid3 VARCHAR(100),
            idfa VARCHAR(100),
            gaid VARCHAR(100),
            payout DECIMAL(10,2),
            ip_address VARCHAR(45),
            gateway_id VARCHAR(100),
            lead_id VARCHAR(100),
            country_iso VARCHAR(2),
            virtual_currency INT DEFAULT 0,
            conversion_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('pending', 'confirmed', 'rejected') DEFAULT 'pending'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createConversionsTable);

    // إنشاء جدول إعدادات Postback إذا لم يكن موجوداً
    $createPostbackSettingsTable = "
        CREATE TABLE IF NOT EXISTS postback_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            postback_url TEXT,
            postback_password VARCHAR(255),
            is_enabled BOOLEAN DEFAULT FALSE,
            whitelist_ip VARCHAR(45) DEFAULT '************',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createPostbackSettingsTable);

    // إنشاء جدول تتبع النقرات والـ IP
    $createClickTrackingTable = "
        CREATE TABLE IF NOT EXISTS click_tracking (
            id INT AUTO_INCREMENT PRIMARY KEY,
            offer_id INT NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            country_code VARCHAR(2),
            click_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_offer_ip (offer_id, ip_address),
            INDEX idx_click_date (click_date),
            FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createClickTrackingTable);

    // إنشاء جدول إعدادات منع التكرار
    $createAntiDuplicateSettingsTable = "
        CREATE TABLE IF NOT EXISTS anti_duplicate_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            is_enabled BOOLEAN DEFAULT TRUE,
            block_duration_days INT DEFAULT 1,
            block_type ENUM('per_offer', 'global') DEFAULT 'per_offer',
            whitelist_ips TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createAntiDuplicateSettingsTable);

    // إدراج إعدادات افتراضية إذا لم تكن موجودة
    $checkSettings = $pdo->query("SELECT COUNT(*) as count FROM anti_duplicate_settings");
    if ($checkSettings->fetch()['count'] == 0) {
        $pdo->exec("INSERT INTO anti_duplicate_settings (is_enabled, block_duration_days, block_type) VALUES (TRUE, 1, 'per_offer')");
    }

    // إنشاء جدول إعدادات الحماية
    $createSiteProtectionTable = "
        CREATE TABLE IF NOT EXISTS site_protection (
            id INT AUTO_INCREMENT PRIMARY KEY,
            is_locked BOOLEAN DEFAULT FALSE,
            password_hash VARCHAR(255),
            lock_message TEXT DEFAULT 'الموقع مغلق مؤقتاً للصيانة',
            allowed_ips TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createSiteProtectionTable);

    // إدراج إعدادات افتراضية للحماية
    $checkProtection = $pdo->query("SELECT COUNT(*) as count FROM site_protection");
    if ($checkProtection->fetch()['count'] == 0) {
        $pdo->exec("INSERT INTO site_protection (is_locked, password_hash, lock_message) VALUES (FALSE, '', 'الموقع مغلق مؤقتاً للصيانة')");
    }
    
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دالة لتنظيف البيانات
function cleanInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة لإدراج مستخدم جديد
function insertUser($pdo, $firstName, $lastName) {
    try {
        $stmt = $pdo->prepare("INSERT INTO users (first_name, last_name) VALUES (?, ?)");
        return $stmt->execute([$firstName, $lastName]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب جميع المستخدمين
function getAllUsers($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM users ORDER BY registration_date DESC");
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لإدراج عرض جديد
function insertOffer($pdo, $name, $link, $image, $description = '', $additional_terms = '', $target_countries = '') {
    try {
        $stmt = $pdo->prepare("INSERT INTO offers (name, link, image, description, additional_terms, target_countries) VALUES (?, ?, ?, ?, ?, ?)");
        return $stmt->execute([$name, $link, $image, $description, $additional_terms, $target_countries]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب جميع العروض
function getAllOffers($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM offers ORDER BY created_date DESC");
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لحذف عرض
function deleteOffer($pdo, $offerId) {
    try {
        $stmt = $pdo->prepare("DELETE FROM offers WHERE id = ?");
        return $stmt->execute([$offerId]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لعد المستخدمين
function countUsers($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        return $result['count'];
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لعد العروض
function countOffers($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM offers");
        $result = $stmt->fetch();
        return $result['count'];
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لإدراج تحويل جديد
function insertConversion($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO conversions
            (campaign_id, campaign_name, subid, subid2, subid3, idfa, gaid, payout, ip_address, gateway_id, lead_id, country_iso, virtual_currency)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        return $stmt->execute([
            $data['campaign_id'] ?? '',
            $data['campaign_name'] ?? '',
            $data['subid'] ?? '',
            $data['subid2'] ?? '',
            $data['subid3'] ?? '',
            $data['idfa'] ?? '',
            $data['gaid'] ?? '',
            $data['payout'] ?? 0,
            $data['ip_address'] ?? '',
            $data['gateway_id'] ?? '',
            $data['lead_id'] ?? '',
            $data['country_iso'] ?? '',
            $data['virtual_currency'] ?? 0
        ]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب جميع التحويلات
function getAllConversions($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM conversions ORDER BY conversion_date DESC");
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لعد التحويلات
function countConversions($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM conversions");
        $result = $stmt->fetch();
        return $result['count'];
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لحفظ إعدادات Postback
function savePostbackSettings($pdo, $url, $password, $enabled) {
    try {
        // حذف الإعدادات القديمة
        $pdo->exec("DELETE FROM postback_settings");

        // إدراج الإعدادات الجديدة
        $stmt = $pdo->prepare("INSERT INTO postback_settings (postback_url, postback_password, is_enabled) VALUES (?, ?, ?)");
        return $stmt->execute([$url, $password, $enabled]);
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لجلب إعدادات Postback
function getPostbackSettings($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM postback_settings ORDER BY id DESC LIMIT 1");
        return $stmt->fetch();
    } catch(PDOException $e) {
        return false;
    }
}

// دالة لحساب إجمالي الأرباح
function getTotalEarnings($pdo) {
    try {
        $stmt = $pdo->query("SELECT SUM(payout) as total FROM conversions WHERE status = 'confirmed'");
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch(PDOException $e) {
        return 0;
    }
}

// دالة لجلب العروض حسب البلد
function getOffersByCountry($pdo, $country_code = '') {
    try {
        if (empty($country_code)) {
            // إذا لم يتم تحديد بلد، اجلب جميع العروض
            $stmt = $pdo->query("SELECT * FROM offers ORDER BY created_date DESC");
            return $stmt->fetchAll();
        } else {
            // جلب العروض التي تستهدف البلد المحدد أو العروض العامة
            $stmt = $pdo->prepare("
                SELECT * FROM offers
                WHERE target_countries IS NULL
                   OR target_countries = ''
                   OR target_countries LIKE ?
                   OR target_countries LIKE ?
                ORDER BY created_date DESC
            ");
            $country_pattern1 = "%$country_code%";
            $country_pattern2 = "%ALL%";
            $stmt->execute([$country_pattern1, $country_pattern2]);
            return $stmt->fetchAll();
        }
    } catch(PDOException $e) {
        return [];
    }
}

// دالة لتحويل رمز البلد إلى اسم البلد
function getCountryName($country_code) {
    $countries = [
        // أمريكا الشمالية
        'US' => 'الولايات المتحدة',
        'CA' => 'كندا',
        'MX' => 'المكسيك',

        // أوروبا
        'GB' => 'المملكة المتحدة',
        'DE' => 'ألمانيا',
        'FR' => 'فرنسا',
        'IT' => 'إيطاليا',
        'ES' => 'إسبانيا',
        'NL' => 'هولندا',
        'BE' => 'بلجيكا',
        'CH' => 'سويسرا',
        'AT' => 'النمسا',
        'SE' => 'السويد',
        'NO' => 'النرويج',
        'DK' => 'الدنمارك',
        'FI' => 'فنلندا',
        'IE' => 'أيرلندا',
        'PT' => 'البرتغال',
        'GR' => 'اليونان',
        'PL' => 'بولندا',
        'CZ' => 'التشيك',
        'HU' => 'المجر',
        'RO' => 'رومانيا',
        'BG' => 'بلغاريا',
        'HR' => 'كرواتيا',
        'SI' => 'سلوفينيا',
        'SK' => 'سلوفاكيا',
        'EE' => 'إستونيا',
        'LV' => 'لاتفيا',
        'LT' => 'ليتوانيا',

        // آسيا والمحيط الهادئ
        'AU' => 'أستراليا',
        'NZ' => 'نيوزيلندا',
        'JP' => 'اليابان',
        'KR' => 'كوريا الجنوبية',
        'SG' => 'سنغافورة',
        'HK' => 'هونغ كونغ',
        'TW' => 'تايوان',
        'MY' => 'ماليزيا',
        'TH' => 'تايلاند',
        'PH' => 'الفلبين',
        'ID' => 'إندونيسيا',
        'VN' => 'فيتنام',
        'IN' => 'الهند',
        'CN' => 'الصين',

        // الشرق الأوسط وأفريقيا
        'AE' => 'الإمارات العربية المتحدة',
        'SA' => 'السعودية',
        'QA' => 'قطر',
        'KW' => 'الكويت',
        'BH' => 'البحرين',
        'OM' => 'عمان',
        'JO' => 'الأردن',
        'LB' => 'لبنان',
        'EG' => 'مصر',
        'MA' => 'المغرب',
        'TN' => 'تونس',
        'DZ' => 'الجزائر',
        'IL' => 'إسرائيل',
        'TR' => 'تركيا',
        'ZA' => 'جنوب أفريقيا',
        'NG' => 'نيجيريا',
        'KE' => 'كينيا',
        'GH' => 'غانا',

        // أمريكا الجنوبية
        'BR' => 'البرازيل',
        'AR' => 'الأرجنتين',
        'CL' => 'تشيلي',
        'CO' => 'كولومبيا',
        'PE' => 'بيرو',
        'VE' => 'فنزويلا',
        'UY' => 'أوروغواي',
        'EC' => 'الإكوادور',

        // خاص
        'ALL' => 'جميع البلدان'
    ];

    return $countries[$country_code] ?? $country_code;
}

// دوال نظام منع التكرار
function getAntiDuplicateSettings($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM anti_duplicate_settings ORDER BY id DESC LIMIT 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$settings) {
            // إعدادات افتراضية
            return [
                'is_enabled' => true,
                'block_duration_days' => 1,
                'block_type' => 'per_offer',
                'whitelist_ips' => ''
            ];
        }

        return $settings;
    } catch(PDOException $e) {
        return [
            'is_enabled' => true,
            'block_duration_days' => 1,
            'block_type' => 'per_offer',
            'whitelist_ips' => ''
        ];
    }
}

function updateAntiDuplicateSettings($pdo, $is_enabled, $block_duration_days, $block_type, $whitelist_ips = '') {
    try {
        // حذف الإعدادات القديمة
        $pdo->exec("DELETE FROM anti_duplicate_settings");

        // إدراج الإعدادات الجديدة
        $stmt = $pdo->prepare("INSERT INTO anti_duplicate_settings (is_enabled, block_duration_days, block_type, whitelist_ips) VALUES (?, ?, ?, ?)");
        return $stmt->execute([$is_enabled, $block_duration_days, $block_type, $whitelist_ips]);
    } catch(PDOException $e) {
        return false;
    }
}

function recordClick($pdo, $offer_id, $ip_address, $user_agent = '', $country_code = '') {
    try {
        $stmt = $pdo->prepare("INSERT INTO click_tracking (offer_id, ip_address, user_agent, country_code) VALUES (?, ?, ?, ?)");
        return $stmt->execute([$offer_id, $ip_address, $user_agent, $country_code]);
    } catch(PDOException $e) {
        return false;
    }
}

function isIPBlocked($pdo, $offer_id, $ip_address) {
    $settings = getAntiDuplicateSettings($pdo);

    // إذا كان النظام معطل
    if (!$settings['is_enabled']) {
        return false;
    }

    // فحص القائمة البيضاء
    if (!empty($settings['whitelist_ips'])) {
        $whitelist = array_map('trim', explode(',', $settings['whitelist_ips']));
        if (in_array($ip_address, $whitelist)) {
            return false;
        }
    }

    try {
        $block_duration = $settings['block_duration_days'];
        $block_date = date('Y-m-d H:i:s', strtotime("-{$block_duration} days"));

        if ($settings['block_type'] === 'global') {
            // منع عالمي - فحص جميع العروض
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM click_tracking WHERE ip_address = ? AND click_date > ?");
            $stmt->execute([$ip_address, $block_date]);
        } else {
            // منع لكل عرض على حدة
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM click_tracking WHERE offer_id = ? AND ip_address = ? AND click_date > ?");
            $stmt->execute([$offer_id, $ip_address, $block_date]);
        }

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;

    } catch(PDOException $e) {
        return false;
    }
}

function getClickStats($pdo) {
    try {
        $stats = [];

        // إجمالي النقرات
        $stmt = $pdo->query("SELECT COUNT(*) as total_clicks FROM click_tracking");
        $stats['total_clicks'] = $stmt->fetch()['total_clicks'];

        // النقرات اليوم
        $stmt = $pdo->query("SELECT COUNT(*) as today_clicks FROM click_tracking WHERE DATE(click_date) = CURDATE()");
        $stats['today_clicks'] = $stmt->fetch()['today_clicks'];

        // النقرات هذا الشهر
        $stmt = $pdo->query("SELECT COUNT(*) as month_clicks FROM click_tracking WHERE YEAR(click_date) = YEAR(CURDATE()) AND MONTH(click_date) = MONTH(CURDATE())");
        $stats['month_clicks'] = $stmt->fetch()['month_clicks'];

        // عدد الـ IPs الفريدة
        $stmt = $pdo->query("SELECT COUNT(DISTINCT ip_address) as unique_ips FROM click_tracking");
        $stats['unique_ips'] = $stmt->fetch()['unique_ips'];

        // النقرات المحجوبة (تقدير)
        $settings = getAntiDuplicateSettings($pdo);
        if ($settings['is_enabled']) {
            $block_duration = $settings['block_duration_days'];
            $block_date = date('Y-m-d H:i:s', strtotime("-{$block_duration} days"));

            if ($settings['block_type'] === 'global') {
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as blocked_clicks
                    FROM click_tracking c1
                    WHERE EXISTS (
                        SELECT 1 FROM click_tracking c2
                        WHERE c2.ip_address = c1.ip_address
                        AND c2.click_date < c1.click_date
                        AND c2.click_date > ?
                    )
                ");
            } else {
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as blocked_clicks
                    FROM click_tracking c1
                    WHERE EXISTS (
                        SELECT 1 FROM click_tracking c2
                        WHERE c2.offer_id = c1.offer_id
                        AND c2.ip_address = c1.ip_address
                        AND c2.click_date < c1.click_date
                        AND c2.click_date > ?
                    )
                ");
            }
            $stmt->execute([$block_date]);
            $stats['blocked_clicks'] = $stmt->fetch()['blocked_clicks'];
        } else {
            $stats['blocked_clicks'] = 0;
        }

        return $stats;
    } catch(PDOException $e) {
        return [
            'total_clicks' => 0,
            'today_clicks' => 0,
            'month_clicks' => 0,
            'unique_ips' => 0,
            'blocked_clicks' => 0
        ];
    }
}

function getRecentClicks($pdo, $limit = 50) {
    try {
        $stmt = $pdo->prepare("
            SELECT ct.*, o.name as offer_name
            FROM click_tracking ct
            LEFT JOIN offers o ON ct.offer_id = o.id
            ORDER BY ct.click_date DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return [];
    }
}

function cleanOldClicks($pdo, $days_to_keep = 30) {
    try {
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days_to_keep} days"));
        $stmt = $pdo->prepare("DELETE FROM click_tracking WHERE click_date < ?");
        return $stmt->execute([$cutoff_date]);
    } catch(PDOException $e) {
        return false;
    }
}

// دوال نظام حماية الموقع
function getSiteProtectionSettings($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM site_protection ORDER BY id DESC LIMIT 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$settings) {
            return [
                'is_locked' => false,
                'password_hash' => '',
                'lock_message' => 'الموقع مغلق مؤقتاً للصيانة',
                'allowed_ips' => ''
            ];
        }

        return $settings;
    } catch(PDOException $e) {
        return [
            'is_locked' => false,
            'password_hash' => '',
            'lock_message' => 'الموقع مغلق مؤقتاً للصيانة',
            'allowed_ips' => ''
        ];
    }
}

function updateSiteProtection($pdo, $is_locked, $password = '', $lock_message = '', $allowed_ips = '') {
    try {
        // حذف الإعدادات القديمة
        $pdo->exec("DELETE FROM site_protection");

        // تشفير كلمة المرور إذا تم توفيرها
        $password_hash = '';
        if (!empty($password)) {
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
        }

        if (empty($lock_message)) {
            $lock_message = 'الموقع مغلق مؤقتاً للصيانة';
        }

        // إدراج الإعدادات الجديدة
        $stmt = $pdo->prepare("INSERT INTO site_protection (is_locked, password_hash, lock_message, allowed_ips) VALUES (?, ?, ?, ?)");
        return $stmt->execute([$is_locked, $password_hash, $lock_message, $allowed_ips]);
    } catch(PDOException $e) {
        return false;
    }
}

function isSiteLocked($pdo) {
    $settings = getSiteProtectionSettings($pdo);
    return $settings['is_locked'];
}

function isIPAllowed($pdo, $ip_address) {
    $settings = getSiteProtectionSettings($pdo);

    // إذا لم يكن الموقع مقفل
    if (!$settings['is_locked']) {
        return true;
    }

    // فحص القائمة البيضاء
    if (!empty($settings['allowed_ips'])) {
        $allowed_ips = array_map('trim', explode(',', $settings['allowed_ips']));
        return in_array($ip_address, $allowed_ips);
    }

    return false;
}

function verifyUnlockPassword($pdo, $password) {
    $settings = getSiteProtectionSettings($pdo);

    if (empty($settings['password_hash'])) {
        return false;
    }

    return password_verify($password, $settings['password_hash']);
}

function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}
?>
