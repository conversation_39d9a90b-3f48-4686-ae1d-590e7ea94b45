<?php
session_start();
require_once 'config.php';
require_once 'site_protection.php';

$message = '';

// معالجة حذف العروض المحددة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_selected'])) {
    if (!empty($_POST['selected_offers'])) {
        $selected_ids = $_POST['selected_offers'];
        $deleted_count = 0;
        
        foreach ($selected_ids as $offer_id) {
            $offer_id = intval($offer_id);
            if ($offer_id > 0) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM offers WHERE id = ?");
                    if ($stmt->execute([$offer_id])) {
                        $deleted_count++;
                    }
                } catch(PDOException $e) {
                    // تجاهل الأخطاء الفردية
                }
            }
        }
        
        if ($deleted_count > 0) {
            $message = '<div class="success">تم حذف ' . $deleted_count . ' عرض بنجاح! ✅</div>';
        } else {
            $message = '<div class="error">فشل في حذف العروض المحددة!</div>';
        }
    } else {
        $message = '<div class="error">يرجى تحديد عروض للحذف!</div>';
    }
}

// معالجة حذف عرض واحد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_single'])) {
    $offer_id = intval($_POST['offer_id']);
    if ($offer_id > 0) {
        try {
            $stmt = $pdo->prepare("DELETE FROM offers WHERE id = ?");
            if ($stmt->execute([$offer_id])) {
                $message = '<div class="success">تم حذف العرض بنجاح! ✅</div>';
            } else {
                $message = '<div class="error">فشل في حذف العرض!</div>';
            }
        } catch(PDOException $e) {
            $message = '<div class="error">خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
        }
    }
}

// جلب جميع العروض
$offers = getAllOffers($pdo);
$total_offers = count($offers);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .stat-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .offer-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .offer-card.selected {
            border-color: #dc3545;
            box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
        }
        
        .offer-checkbox {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 20px;
            height: 20px;
            z-index: 2;
            transform: scale(1.2);
        }
        
        .offer-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }
        
        .offer-content {
            padding: 20px;
            padding-top: 50px;
        }
        
        .offer-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .offer-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .offer-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .bulk-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
        }
        
        .bulk-actions h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .bulk-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .select-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .select-btn {
            background: #6c757d;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .select-btn:hover {
            background: #5a6268;
        }
        
        .selected-count {
            background: #28a745;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .delete-selected-btn {
            background: #dc3545;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .delete-selected-btn:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .delete-selected-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .home-link {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            font-size: 16px;
        }
        
        .home-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Tooltip styles for countries */
        .country-tooltip, .country-more-tooltip {
            cursor: help;
            position: relative;
            border-bottom: 1px dotted #007bff;
            transition: all 0.3s ease;
        }

        .country-tooltip:hover, .country-more-tooltip:hover {
            border-bottom-color: #0056b3;
            color: #0056b3;
        }

        .country-tooltip[title]:hover::after,
        .country-more-tooltip[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            animation: tooltipFadeIn 0.3s ease;
            margin-bottom: 5px;
            max-width: 250px;
            white-space: normal;
            text-align: center;
            line-height: 1.4;
        }

        .country-tooltip[title]:hover::before,
        .country-more-tooltip[title]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(1px);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1001;
        }

        @keyframes tooltipFadeIn {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(5px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ إدارة العروض</h1>
            <p>تحديد وحذف العروض الحالية</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
                <a href="manage_offers.php" class="nav-link">🗂️ إدارة العروض</a>
                <a href="anti_duplicate_admin.php" class="nav-link">🛡️ منع التكرار</a>
                <a href="site_protection_admin.php" class="nav-link">🔑 كلمة مرور الموقع</a>
                <a href="postback_admin.php" class="nav-link">📊 Postback</a>
            </div>
        </div>
        
        <!-- إحصائيات العروض -->
        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number"><?php echo $total_offers; ?></div>
                <div class="stat-label">إجمالي العروض</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🗑️</div>
                <div class="stat-number" id="selected-count">0</div>
                <div class="stat-label">عروض محددة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚡</div>
                <div class="stat-number">سريع</div>
                <div class="stat-label">حذف جماعي</div>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <?php if (!empty($offers)): ?>
        <div class="form-section">
            <h2>📋 قائمة العروض الحالية</h2>
            
            <!-- أدوات التحديد الجماعي -->
            <div class="bulk-actions">
                <h3>🔧 أدوات التحديد والحذف</h3>
                <form method="POST" id="bulk-form">
                    <div class="bulk-controls">
                        <div class="select-controls">
                            <button type="button" class="select-btn" onclick="selectAll()">تحديد الكل</button>
                            <button type="button" class="select-btn" onclick="selectNone()">إلغاء التحديد</button>
                            <button type="button" class="select-btn" onclick="selectInverse()">عكس التحديد</button>
                        </div>
                        
                        <div class="selected-count" id="selected-display">
                            لم يتم تحديد أي عرض
                        </div>
                        
                        <button type="submit" 
                                name="delete_selected" 
                                class="delete-selected-btn" 
                                id="delete-btn"
                                disabled
                                onclick="return confirm('هل أنت متأكد من حذف العروض المحددة؟ هذا الإجراء لا يمكن التراجع عنه!')">
                            🗑️ حذف المحدد
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- شبكة العروض -->
            <div class="offers-grid">
                <?php foreach ($offers as $offer): ?>
                <div class="offer-card" id="offer-<?php echo $offer['id']; ?>">
                    <input type="checkbox" 
                           class="offer-checkbox" 
                           name="selected_offers[]" 
                           value="<?php echo $offer['id']; ?>"
                           form="bulk-form"
                           onchange="updateSelection()">
                    
                    <img src="<?php echo htmlspecialchars($offer['image']); ?>"
                         alt="<?php echo htmlspecialchars($offer['name']); ?>"
                         class="offer-image"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzUwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPtit2YXZhNin2YQg2KfZhNi12YjYsdipPC90ZXh0Pjwvc3ZnPg=='">
                    
                    <div class="offer-content">
                        <div class="offer-name"><?php echo htmlspecialchars($offer['name']); ?></div>
                        
                        <?php if (!empty($offer['description'])): ?>
                        <div class="offer-info">
                            <strong>الوصف:</strong> <?php echo htmlspecialchars(substr($offer['description'], 0, 100)); ?><?php echo strlen($offer['description']) > 100 ? '...' : ''; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($offer['target_countries'])): ?>
                        <div class="offer-info">
                            <strong>البلدان:</strong>
                            <?php
                            if ($offer['target_countries'] === 'ALL') {
                                echo '<span class="country-tooltip" title="متاح لجميع البلدان">🌐 جميع البلدان</span>';
                            } else {
                                $countries = explode(',', $offer['target_countries']);
                                $countries = array_map('trim', $countries);
                                $displayed_countries = array_slice($countries, 0, 6);
                                $country_items = [];

                                foreach ($displayed_countries as $country) {
                                    $country_name = getCountryName($country);
                                    $country_items[] = '<span class="country-tooltip" title="' . htmlspecialchars($country_name) . '">' . htmlspecialchars($country) . '</span>';
                                }

                                echo implode(', ', $country_items);

                                if (count($countries) > 6) {
                                    $remaining_countries = array_slice($countries, 6);
                                    $remaining_names = array_map('getCountryName', $remaining_countries);
                                    $tooltip_text = implode('، ', $remaining_names);
                                    echo ', <span class="country-more-tooltip" title="' . htmlspecialchars($tooltip_text) . '">+' . (count($countries) - 6) . ' أخرى</span>';
                                }
                            }
                            ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="offer-info">
                            <strong>تاريخ الإضافة:</strong> <?php echo date('Y-m-d H:i', strtotime($offer['created_date'])); ?>
                        </div>
                        
                        <div class="offer-actions">
                            <a href="<?php echo htmlspecialchars($offer['link']); ?>" 
                               target="_blank" 
                               class="btn btn-primary">🔗 عرض</a>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                <button type="submit" 
                                        name="delete_single" 
                                        class="btn btn-danger"
                                        onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                    🗑️ حذف
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <?php else: ?>
        <div class="form-section">
            <div style="text-align: center; padding: 50px; color: #666;">
                <h2>📭 لا توجد عروض حالياً</h2>
                <p>يمكنك إضافة عروض جديدة من لوحة التحكم أو جلبها من API</p>
                <div style="margin-top: 20px;">
                    <a href="admin.php" class="btn btn-primary">⚙️ لوحة التحكم</a>
                    <a href="api_offers.php" class="btn btn-primary">🌐 جلب من API</a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- زر عائم للصفحة الرئيسية -->
    <a href="index.php" class="home-link">🏠 الرئيسية</a>
    
    <script>
        function selectAll() {
            const checkboxes = document.querySelectorAll('.offer-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            updateSelection();
        }
        
        function selectNone() {
            const checkboxes = document.querySelectorAll('.offer-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            updateSelection();
        }
        
        function selectInverse() {
            const checkboxes = document.querySelectorAll('.offer-checkbox');
            checkboxes.forEach(cb => cb.checked = !cb.checked);
            updateSelection();
        }
        
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.offer-checkbox');
            const checked = document.querySelectorAll('.offer-checkbox:checked');
            const count = checked.length;
            
            // تحديث العداد
            document.getElementById('selected-count').textContent = count;
            document.getElementById('selected-display').textContent = 
                count === 0 ? 'لم يتم تحديد أي عرض' : `تم تحديد ${count} عرض`;
            
            // تفعيل/تعطيل زر الحذف
            const deleteBtn = document.getElementById('delete-btn');
            deleteBtn.disabled = count === 0;
            
            // تحديث مظهر البطاقات
            checkboxes.forEach(cb => {
                const card = cb.closest('.offer-card');
                if (cb.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        }
        
        // تحديث التحديد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateSelection);
    </script>
</body>
</html>
