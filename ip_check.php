<?php
session_start();

// مفتاح API الخاص بـ IP Quality Score
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';

// الحصول على IP الخاص بالمستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$check_ip = $user_ip;
$ip_data = null;
$error_message = '';

// جلب إحصائيات استهلاك API
$api_usage = getApiUsageStats();

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['check_ip'])) {
    $check_ip = trim($_POST['ip_address']);
    if (empty($check_ip)) {
        $check_ip = $user_ip;
    }
}

// فحص الـ IP باستخدام API
if (!empty($check_ip)) {
    $api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$check_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $response = @file_get_contents($api_url, false, $context);
    
    if ($response !== false) {
        $ip_data = json_decode($response, true);
        if (!$ip_data || (isset($ip_data['success']) && !$ip_data['success'])) {
            $error_message = 'فشل في الحصول على بيانات الـ IP. يرجى المحاولة مرة أخرى.';
            $ip_data = null;
        } else {
            // تسجيل استهلاك API عند النجاح
            logApiUsage();
        }
    } else {
        $error_message = 'خطأ في الاتصال بخدمة فحص الـ IP.';
    }
}

// دالة لتحديد مستوى الخطر (0-28 آمن)
function getRiskLevel($fraud_score) {
    if ($fraud_score >= 85) return ['عالي جداً', '#dc3545', '🚨'];
    if ($fraud_score >= 75) return ['عالي', '#fd7e14', '⚠️'];
    if ($fraud_score >= 50) return ['متوسط', '#ffc107', '⚡'];
    if ($fraud_score >= 29) return ['منخفض', '#28a745', '✅'];
    return ['آمن', '#20c997', '🛡️'];
}

// دالة لتحويل القيم المنطقية
function getBooleanText($value) {
    return $value ? 'نعم' : 'لا';
}

// دالة لتسجيل استهلاك API
function logApiUsage() {
    $log_file = 'api_usage.txt';
    $current_time = date('Y-m-d H:i:s');
    $log_entry = $current_time . "\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// دالة لحساب استهلاك API
function getApiUsageStats() {
    $log_file = 'api_usage.txt';
    if (!file_exists($log_file)) {
        return ['today' => 0, 'this_month' => 0, 'total' => 0];
    }

    $logs = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $today = date('Y-m-d');
    $this_month = date('Y-m');

    $today_count = 0;
    $month_count = 0;
    $total_count = count($logs);

    foreach ($logs as $log) {
        if (strpos($log, $today) === 0) {
            $today_count++;
        }
        if (strpos($log, $this_month) === 0) {
            $month_count++;
        }
    }

    return [
        'today' => $today_count,
        'this_month' => $month_count,
        'total' => $total_count
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص جودة الـ IP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .results-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .risk-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .info-value {
            color: #666;
            font-size: 14px;
        }
        
        .current-ip {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #2196f3;
        }

        .api-usage-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }

        .usage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .usage-stat {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .usage-number {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }

        .usage-label {
            color: #666;
            font-size: 14px;
        }

        .home-link {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            font-size: 16px;
        }

        .home-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص جودة الـ IP</h1>
            <p>تحقق من جودة وأمان عنوان الـ IP</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="ip_check.php" class="nav-link">🔍 فحص الـ IP</a>
            </div>
        </div>

        <!-- إحصائيات استهلاك API -->
        <div class="form-section">
            <div class="api-usage-section">
                <h3 style="margin: 0 0 10px 0; color: #333;">📊 إحصائيات استهلاك API</h3>
                <p style="margin: 0 0 15px 0; color: #666; font-size: 14px;">
                    تتبع استخدام IP Quality Score API
                </p>
                <div class="usage-stats">
                    <div class="usage-stat">
                        <div class="usage-number"><?php echo $api_usage['today']; ?></div>
                        <div class="usage-label">اليوم</div>
                    </div>
                    <div class="usage-stat">
                        <div class="usage-number"><?php echo $api_usage['this_month']; ?></div>
                        <div class="usage-label">هذا الشهر</div>
                    </div>
                    <div class="usage-stat">
                        <div class="usage-number"><?php echo $api_usage['total']; ?></div>
                        <div class="usage-label">الإجمالي</div>
                    </div>
                    <div class="usage-stat">
                        <div class="usage-number"><?php echo max(0, 5000 - $api_usage['this_month']); ?></div>
                        <div class="usage-label">المتبقي (5K)</div>
                    </div>
                </div>

                <?php if ($api_usage['this_month'] > 4500): ?>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin-top: 15px; text-align: center;">
                    <strong>⚠️ تحذير:</strong> اقتربت من الحد الأقصى الشهري (5000 طلب)
                </div>
                <?php elseif ($api_usage['this_month'] >= 5000): ?>
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 5px; margin-top: 15px; text-align: center;">
                    <strong>🚫 تنبيه:</strong> تم الوصول للحد الأقصى الشهري
                </div>
                <?php endif; ?>

                <div style="text-align: center; margin-top: 15px;">
                    <a href="api_usage_details.php" style="color: #667eea; text-decoration: none; font-weight: 600;">
                        📊 عرض التفاصيل الكاملة
                    </a>
                </div>
            </div>
        </div>

        <div class="form-section">
            <h2>🌐 فحص عنوان IP</h2>

            <div class="current-ip">
                <strong>عنوان الـ IP الحالي الخاص بك: <?php echo htmlspecialchars($user_ip); ?></strong>
            </div>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #333;">📊 مستويات تقييم الخطر:</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
                    <div><span style="color: #20c997;">🛡️ آمن:</span> 0-28%</div>
                    <div><span style="color: #28a745;">✅ منخفض:</span> 29-49%</div>
                    <div><span style="color: #ffc107;">⚡ متوسط:</span> 50-74%</div>
                    <div><span style="color: #fd7e14;">⚠️ عالي:</span> 75-84%</div>
                    <div><span style="color: #dc3545;">🚨 عالي جداً:</span> 85%+</div>
                </div>
            </div>

            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2196f3;">
                <strong>📊 نظام التقييم:</strong><br>
                • 🛡️ آمن (0-7): IP موثوق وآمن<br>
                • ✅ منخفض (8-14): مخاطر قليلة<br>
                • ⚡ متوسط (15-19): يتطلب حذر<br>
                • ⚠️ عالي (20-24): مخاطر عالية<br>
                • 🚨 عالي جداً (25-28): مخاطر شديدة
            </div>
            
            <?php if (!empty($error_message)): ?>
                <div class="error"><?php echo $error_message; ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="ip_address">عنوان الـ IP للفحص:</label>
                    <input type="text" 
                           id="ip_address" 
                           name="ip_address" 
                           value="<?php echo htmlspecialchars($check_ip); ?>"
                           placeholder="مثال: ******* أو اتركه فارغاً لفحص IP الحالي">
                </div>
                
                <button type="submit" name="check_ip">🔍 فحص الـ IP</button>
            </form>
        </div>
        
        <?php if ($ip_data && !$error_message): ?>
        <div class="results-section">
            <h2>📊 نتائج الفحص لـ: <?php echo htmlspecialchars($check_ip); ?></h2>
            
            <?php 
            $risk_info = getRiskLevel($ip_data['fraud_score'] ?? 0);
            ?>
            
            <div class="risk-badge" style="background-color: <?php echo $risk_info[1]; ?>;">
                <?php echo $risk_info[2]; ?> مستوى الخطر: <?php echo $risk_info[0]; ?> (<?php echo $ip_data['fraud_score'] ?? 0; ?>%)
            </div>

            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                <strong>ℹ️ معلومة:</strong> النطاق 0-28 يُعتبر آمناً تماماً
            </div>
            
            <div class="info-grid">
                <div class="info-card">
                    <div class="info-title">🌍 الموقع الجغرافي</div>
                    <div class="info-value">
                        <?php echo htmlspecialchars(($ip_data['city'] ?? 'غير محدد') . ', ' . ($ip_data['region'] ?? 'غير محدد') . ', ' . ($ip_data['country_code'] ?? 'غير محدد')); ?>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🏢 مزود الخدمة</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['ISP'] ?? 'غير محدد'); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🏛️ المنظمة</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['organization'] ?? 'غير محدد'); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">📱 جهاز محمول</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['mobile'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🔒 بروكسي/VPN</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['proxy'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🌐 Tor</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['tor'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🤖 بوت مشبوه</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['bot_status'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">⏰ المنطقة الزمنية</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['timezone'] ?? 'غير محدد'); ?></div>
                </div>

                <div class="info-card">
                    <div class="info-title">🔢 نقاط الاحتيال</div>
                    <div class="info-value"><?php echo ($ip_data['fraud_score'] ?? 0); ?> من 28</div>
                </div>

                <div class="info-card">
                    <div class="info-title">📡 نوع الاتصال</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['connection_type'] ?? 'غير محدد'); ?></div>
                </div>

                <div class="info-card">
                    <div class="info-title">🌐 استخدام عام</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['recent_abuse'] ?? false); ?></div>
                </div>
            </div>

            <?php if (($ip_data['fraud_score'] ?? 0) > 15): ?>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>⚠️ تحذير:</strong> هذا الـ IP يحمل مخاطر متوسطة إلى عالية. يُنصح بالحذر عند التعامل معه.
            </div>
            <?php elseif (($ip_data['fraud_score'] ?? 0) <= 7): ?>
            <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>✅ آمن:</strong> هذا الـ IP يبدو آمناً وموثوقاً للاستخدام.
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- زر عائم للصفحة الرئيسية -->
    <a href="index.php" class="home-link">🏠 الرئيسية</a>
</body>
</html>
