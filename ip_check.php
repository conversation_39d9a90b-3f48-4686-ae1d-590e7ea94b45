<?php
session_start();

// مفتاح API الخاص بـ IP Quality Score
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';

// الحصول على IP الخاص بالمستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$check_ip = $user_ip;
$ip_data = null;
$error_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['check_ip'])) {
    $check_ip = trim($_POST['ip_address']);
    if (empty($check_ip)) {
        $check_ip = $user_ip;
    }
}

// فحص الـ IP باستخدام API
if (!empty($check_ip)) {
    $api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$check_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $response = @file_get_contents($api_url, false, $context);
    
    if ($response !== false) {
        $ip_data = json_decode($response, true);
        if (!$ip_data || (isset($ip_data['success']) && !$ip_data['success'])) {
            $error_message = 'فشل في الحصول على بيانات الـ IP. يرجى المحاولة مرة أخرى.';
            $ip_data = null;
        }
    } else {
        $error_message = 'خطأ في الاتصال بخدمة فحص الـ IP.';
    }
}

// دالة لتحديد مستوى الخطر
function getRiskLevel($fraud_score) {
    if ($fraud_score >= 85) return ['عالي جداً', '#dc3545', '🚨'];
    if ($fraud_score >= 75) return ['عالي', '#fd7e14', '⚠️'];
    if ($fraud_score >= 50) return ['متوسط', '#ffc107', '⚡'];
    if ($fraud_score >= 25) return ['منخفض', '#28a745', '✅'];
    return ['آمن', '#20c997', '🛡️'];
}

// دالة لتحويل القيم المنطقية
function getBooleanText($value) {
    return $value ? 'نعم' : 'لا';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص جودة الـ IP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .results-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .risk-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .info-value {
            color: #666;
            font-size: 14px;
        }
        
        .current-ip {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص جودة الـ IP</h1>
            <p>تحقق من جودة وأمان عنوان الـ IP</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="ip_check.php" class="nav-link">🔍 فحص الـ IP</a>
            </div>
        </div>
        
        <div class="form-section">
            <h2>🌐 فحص عنوان IP</h2>
            
            <div class="current-ip">
                <strong>عنوان الـ IP الحالي الخاص بك: <?php echo htmlspecialchars($user_ip); ?></strong>
            </div>
            
            <?php if (!empty($error_message)): ?>
                <div class="error"><?php echo $error_message; ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="ip_address">عنوان الـ IP للفحص:</label>
                    <input type="text" 
                           id="ip_address" 
                           name="ip_address" 
                           value="<?php echo htmlspecialchars($check_ip); ?>"
                           placeholder="مثال: ******* أو اتركه فارغاً لفحص IP الحالي">
                </div>
                
                <button type="submit" name="check_ip">🔍 فحص الـ IP</button>
            </form>
        </div>
        
        <?php if ($ip_data && !$error_message): ?>
        <div class="results-section">
            <h2>📊 نتائج الفحص لـ: <?php echo htmlspecialchars($check_ip); ?></h2>
            
            <?php 
            $risk_info = getRiskLevel($ip_data['fraud_score'] ?? 0);
            ?>
            
            <div class="risk-badge" style="background-color: <?php echo $risk_info[1]; ?>;">
                <?php echo $risk_info[2]; ?> مستوى الخطر: <?php echo $risk_info[0]; ?> (<?php echo $ip_data['fraud_score'] ?? 0; ?>%)
            </div>
            
            <div class="info-grid">
                <div class="info-card">
                    <div class="info-title">🌍 الموقع الجغرافي</div>
                    <div class="info-value">
                        <?php echo htmlspecialchars(($ip_data['city'] ?? 'غير محدد') . ', ' . ($ip_data['region'] ?? 'غير محدد') . ', ' . ($ip_data['country_code'] ?? 'غير محدد')); ?>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🏢 مزود الخدمة</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['ISP'] ?? 'غير محدد'); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🏛️ المنظمة</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['organization'] ?? 'غير محدد'); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">📱 جهاز محمول</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['mobile'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🔒 بروكسي/VPN</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['proxy'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🌐 Tor</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['tor'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">🤖 بوت مشبوه</div>
                    <div class="info-value"><?php echo getBooleanText($ip_data['bot_status'] ?? false); ?></div>
                </div>
                
                <div class="info-card">
                    <div class="info-title">⏰ المنطقة الزمنية</div>
                    <div class="info-value"><?php echo htmlspecialchars($ip_data['timezone'] ?? 'غير محدد'); ?></div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
