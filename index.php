<?php
session_start();
require_once 'config.php';

// جلب العروض من قاعدة البيانات
$offers = getAllOffers($pdo);

// الحصول على IP المستخدم وفحص مستوى الخطر
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$fraud_score = null;

// فحص مستوى الخطر للـ IP
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&fast=true";

$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);
if ($response !== false) {
    $ip_data = json_decode($response, true);
    if ($ip_data && isset($ip_data['fraud_score'])) {
        $fraud_score = $ip_data['fraud_score'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - العروض والخدمات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        


        .temp-mail-section {
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            text-align: center;
        }

        .temp-mail-title {
            color: white;
            font-size: 1.5em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .temp-mail-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .temp-mail-link {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 120px;
        }

        .temp-mail-link:hover {
            background: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .temp-mail-description {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-bottom: 20px;
        }

        .offers-section {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .offers-title {
            color: #333;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }

        .offer-card {
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .offer-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .offer-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .offer-content {
            padding: 20px;
        }

        .offer-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .offer-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }

        .offer-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .admin-link {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .admin-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 مرحباً بك في موقعنا</h1>
        <p style="text-align: center; color: #666; font-size: 1.2em; margin-bottom: 20px;">
            اكتشف أفضل العروض والخدمات المتاحة
        </p>

        <!-- عرض مستوى الخطر -->
        <?php if ($fraud_score !== null): ?>
        <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-block; background: <?php
                if ($fraud_score <= 28) echo '#20c997';
                elseif ($fraud_score <= 49) echo '#28a745';
                elseif ($fraud_score <= 74) echo '#ffc107';
                elseif ($fraud_score <= 84) echo '#fd7e14';
                else echo '#dc3545';
            ?>; color: white; padding: 15px 25px; border-radius: 25px; font-weight: 600; font-size: 1.1em;">
                🛡️ مستوى الأمان: <?php echo $fraud_score; ?>%
            </div>
        </div>
        <?php endif; ?>



        <!-- قسم مواقع البريد المؤقت -->
        <div class="temp-mail-section">
            <div class="temp-mail-title">📧 مواقع البريد المؤقت</div>
            <div class="temp-mail-description">
                احصل على بريد إلكتروني مؤقت مجاني للاستخدام السريع
            </div>
            <div class="temp-mail-links">
                <a href="https://temp-mail.org/en/" target="_blank" class="temp-mail-link">
                    🌟 Temp Mail
                </a>
                <a href="https://www.run2mail.com/en" target="_blank" class="temp-mail-link">
                    ⚡ Run2Mail
                </a>
                <a href="https://tempail.com/" target="_blank" class="temp-mail-link">
                    🚀 Tempail
                </a>
            </div>
        </div>

        <!-- قسم العروض -->
        <?php if (!empty($offers)): ?>
        <div class="offers-section">
            <h2 class="offers-title">🎯 العروض المميزة</h2>
            <div class="offers-grid">
                <?php foreach ($offers as $offer): ?>
                <div class="offer-card">
                    <img src="<?php echo htmlspecialchars($offer['image']); ?>"
                         alt="<?php echo htmlspecialchars($offer['name']); ?>"
                         class="offer-image"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPtit2YXZhNin2YQg2KfZhNi12YjYsdipPC90ZXh0Pjwvc3ZnPg=='">

                    <div class="offer-content">
                        <div class="offer-name"><?php echo htmlspecialchars($offer['name']); ?></div>
                        <a href="<?php echo htmlspecialchars($offer['link']); ?>"
                           target="_blank"
                           class="offer-btn">
                            🔗 مشاهدة العرض
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>



        <!-- روابط الإدارة -->
        <a href="admin.php" class="admin-link">⚙️ لوحة التحكم</a>
        <a href="api_offers.php" class="admin-link" style="bottom: 80px;">🌐 عروض API</a>
    </div>
</body>
</html>
