<?php
session_start();
require_once 'config.php';

// جلب بلد المستخدم من IP
$user_country = null;

// الحصول على IP المستخدم وفحص مستوى الخطر
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$user_ip = getUserIP();
$fraud_score = null;

// فحص مستوى الخطر للـ IP
$api_key = 'GQ4YylpmHC1eMvwX7QBGxq4S3DwAfqXV';
$api_url = "https://ipqualityscore.com/api/json/ip/$api_key/$user_ip?strictness=1&allow_public_access_points=true&fast=true&lighter_penalties=true&mobile=true";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($api_url, false, $context);
if ($response !== false) {
    $ip_data = json_decode($response, true);
    if ($ip_data && isset($ip_data['success']) && $ip_data['success']) {
        if (isset($ip_data['fraud_score'])) {
            $fraud_score = $ip_data['fraud_score'];
        }
        if (isset($ip_data['country_code'])) {
            $user_country = $ip_data['country_code'];
        }
    }

    // تشخيص للمطورين (يمكن إزالته لاحقاً)
    if (isset($_GET['debug'])) {
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px; font-size: 12px;'>";
        echo "IP: $user_ip\n";
        echo "Country: $user_country\n";
        echo "API URL: $api_url\n";
        echo "Response: " . print_r($ip_data, true);
        echo "</pre>";
    }
}

// جلب العروض حسب بلد المستخدم
$offers = getOffersByCountry($pdo, $user_country);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - العروض والخدمات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        


        .temp-mail-section {
            margin-top: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 12px;
            text-align: center;
        }

        .temp-mail-title {
            color: white;
            font-size: 1.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .temp-mail-links {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .temp-mail-link {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 100px;
            font-size: 14px;
        }

        .temp-mail-link:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .temp-mail-description {
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            margin-bottom: 15px;
        }

        .offers-section {
            margin-top: 25px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .offers-title {
            color: #333;
            font-size: 1.6em;
            margin-bottom: 18px;
            text-align: center;
            font-weight: 600;
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .offer-card {
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .offer-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .offer-image {
            width: 100%;
            height: 140px;
            object-fit: cover;
        }

        .offer-content {
            padding: 15px;
        }

        .offer-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .offer-description {
            color: #666;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 10px;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border-left: 2px solid #667eea;
        }

        .offer-terms {
            color: #856404;
            font-size: 12px;
            line-height: 1.3;
            margin-bottom: 12px;
            background: #fff3cd;
            padding: 6px;
            border-radius: 4px;
            border-left: 2px solid #ffc107;
        }

        .offer-terms strong {
            color: #721c24;
        }

        .country-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
            z-index: 2;
        }

        .offer-card {
            position: relative;
        }

        .user-country-info {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }

        .offer-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            text-align: center;
        }

        .offer-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .admin-link {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .admin-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 1024px) {
            .offers-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .offers-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .admin-link {
                bottom: 20px;
                right: 20px;
                padding: 12px 20px;
                font-size: 14px;
            }

            .offer-image {
                height: 120px;
            }

            .offer-content {
                padding: 12px;
            }

            .offer-name {
                font-size: 1em;
                margin-bottom: 10px;
            }

            .temp-mail-section {
                padding: 12px;
                margin-top: 15px;
            }

            .temp-mail-title {
                font-size: 1.1em;
                margin-bottom: 8px;
            }

            .temp-mail-links {
                gap: 8px;
            }

            .temp-mail-link {
                padding: 6px 12px;
                min-width: 80px;
                font-size: 13px;
            }

            .temp-mail-description {
                font-size: 12px;
                margin-bottom: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 مرحباً بك في موقعنا</h1>
        <p style="text-align: center; color: #666; font-size: 1.2em; margin-bottom: 20px;">
            اكتشف أفضل العروض والخدمات المتاحة
        </p>

        <!-- عرض مستوى الخطر -->
        <?php if ($fraud_score !== null): ?>
        <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-block; background: <?php
                if ($fraud_score <= 28) echo '#20c997';
                elseif ($fraud_score <= 49) echo '#28a745';
                elseif ($fraud_score <= 74) echo '#ffc107';
                elseif ($fraud_score <= 84) echo '#fd7e14';
                else echo '#dc3545';
            ?>; color: white; padding: 15px 25px; border-radius: 25px; font-weight: 600; font-size: 1.1em; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                🛡️ مستوى الأمان: <?php echo $fraud_score; ?>%
                <?php
                if ($fraud_score <= 28) echo '(ممتاز)';
                elseif ($fraud_score <= 49) echo '(جيد)';
                elseif ($fraud_score <= 74) echo '(متوسط)';
                elseif ($fraud_score <= 84) echo '(عالي)';
                else echo '(خطر)';
                ?>
            </div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                IP: <?php echo htmlspecialchars($user_ip); ?>
            </div>
        </div>
        <?php else: ?>
        <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-block; background: #6c757d; color: white; padding: 15px 25px; border-radius: 25px; font-weight: 600; font-size: 1.1em;">
                🔍 فحص مستوى الأمان...
            </div>
        </div>
        <?php endif; ?>

        <!-- معلومات بلد المستخدم -->
        <?php if ($user_country): ?>
        <div class="user-country-info">
            <strong>🌍 بلدك:</strong> <?php echo getCountryName($user_country); ?> (<?php echo $user_country; ?>)
            <br>
            <small>العروض المعروضة مخصصة لبلدك</small>
        </div>
        <?php endif; ?>

        <!-- قسم مواقع البريد المؤقت -->
        <div class="temp-mail-section">
            <div class="temp-mail-title">📧 مواقع البريد المؤقت</div>
            <div class="temp-mail-description">
                احصل على بريد إلكتروني مؤقت مجاني للاستخدام السريع
            </div>
            <div class="temp-mail-links">
                <a href="https://temp-mail.org/en/" target="_blank" class="temp-mail-link">
                    🌟 Temp Mail
                </a>
                <a href="https://www.run2mail.com/en" target="_blank" class="temp-mail-link">
                    ⚡ Run2Mail
                </a>
                <a href="https://tempail.com/" target="_blank" class="temp-mail-link">
                    🚀 Tempail
                </a>
            </div>
        </div>

        <!-- قسم العروض -->
        <?php if (!empty($offers)): ?>
        <div class="offers-section">
            <h2 class="offers-title">🎯 العروض المميزة</h2>
            <div class="offers-grid">
                <?php foreach ($offers as $offer): ?>
                <div class="offer-card">
                    <!-- شارة البلدان المستهدفة -->
                    <?php if (!empty($offer['target_countries'])): ?>
                    <div class="country-badge">
                        <?php
                        $countries = explode(',', $offer['target_countries']);
                        echo implode(', ', array_map('trim', $countries));
                        ?>
                    </div>
                    <?php endif; ?>

                    <img src="<?php echo htmlspecialchars($offer['image']); ?>"
                         alt="<?php echo htmlspecialchars($offer['name']); ?>"
                         class="offer-image"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPtit2YXZhNin2YQg2KfZhNi12YjYsdipPC90ZXh0Pjwvc3ZnPg=='">

                    <div class="offer-content">
                        <div class="offer-name"><?php echo htmlspecialchars($offer['name']); ?></div>

                        <?php if (!empty($offer['description'])): ?>
                        <div class="offer-description">
                            <strong>📝 الوصف:</strong><br>
                            <?php echo htmlspecialchars(substr($offer['description'], 0, 120)); ?><?php echo strlen($offer['description']) > 120 ? '...' : ''; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($offer['additional_terms'])): ?>
                        <div class="offer-terms">
                            <strong>⚠️ شروط إضافية:</strong><br>
                            <?php echo htmlspecialchars(substr($offer['additional_terms'], 0, 100)); ?><?php echo strlen($offer['additional_terms']) > 100 ? '...' : ''; ?>
                        </div>
                        <?php endif; ?>

                        <a href="<?php echo htmlspecialchars($offer['link']); ?>"
                           target="_blank"
                           class="offer-btn">
                            🔗 مشاهدة العرض
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>



        <!-- روابط الإدارة -->
        <a href="admin.php" class="admin-link">⚙️ لوحة التحكم</a>
        <a href="api_offers.php" class="admin-link" style="bottom: 80px;">🌐 عروض API</a>
    </div>
</body>
</html>
