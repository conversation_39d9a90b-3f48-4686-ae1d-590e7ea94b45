<?php
session_start();
require_once 'config.php';

// قائمة الأسماء الإنجليزية للمولد العشوائي
$firstNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

$lastNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

// معالجة النموذج
$message = '';

// جلب المستخدمين والعروض من قاعدة البيانات
$registeredUsers = getAllUsers($pdo);
$offers = getAllOffers($pdo);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['register'])) {
        $firstName = cleanInput($_POST['first_name']);
        $lastName = cleanInput($_POST['last_name']);

        if (!empty($firstName) && !empty($lastName)) {
            if (insertUser($pdo, $firstName, $lastName)) {
                $message = '<div class="success">تم التسجيل بنجاح! مرحباً ' . htmlspecialchars($firstName . ' ' . $lastName) . '</div>';
                // إعادة جلب المستخدمين لعرض التحديث
                $registeredUsers = getAllUsers($pdo);
            } else {
                $message = '<div class="error">حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى!</div>';
            }
        } else {
            $message = '<div class="error">يرجى ملء جميع الحقول!</div>';
        }
    } elseif (isset($_POST['generate'])) {
        $randomFirstName = $firstNames[array_rand($firstNames)];
        $randomLastName = $lastNames[array_rand($lastNames)];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل السريع</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #ddd;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .users-list {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }
        
        .user-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .user-name {
            font-weight: 600;
            color: #333;
        }
        
        .user-email {
            color: #666;
            font-size: 14px;
        }
        
        .user-date {
            color: #999;
            font-size: 12px;
        }

        .temp-mail-section {
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            text-align: center;
        }

        .temp-mail-title {
            color: white;
            font-size: 1.5em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .temp-mail-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .temp-mail-link {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 120px;
        }

        .temp-mail-link:hover {
            background: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .temp-mail-description {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-bottom: 20px;
        }

        .offers-section {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .offers-title {
            color: #333;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }

        .offer-card {
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .offer-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .offer-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .offer-content {
            padding: 20px;
        }

        .offer-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .offer-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }

        .offer-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .admin-link {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .admin-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 التسجيل السريع</h1>
        
        <?php echo $message; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="first_name">الاسم الأول (بالإنجليزية):</label>
                <input type="text" 
                       id="first_name" 
                       name="first_name" 
                       value="<?php echo isset($randomFirstName) ? htmlspecialchars($randomFirstName) : ''; ?>"
                       placeholder="مثال: John"
                       required>
            </div>
            
            <div class="form-group">
                <label for="last_name">اسم العائلة (بالإنجليزية):</label>
                <input type="text"
                       id="last_name"
                       name="last_name"
                       value="<?php echo isset($randomLastName) ? htmlspecialchars($randomLastName) : ''; ?>"
                       placeholder="مثال: Smith"
                       required>
            </div>
            
            <div class="button-group">
                <button type="submit" name="register" class="btn-primary">
                    ✅ تسجيل
                </button>
                <button type="submit" name="generate" class="btn-secondary">
                    🎲 اسم عشوائي
                </button>
            </div>
        </form>

        <!-- قسم مواقع البريد المؤقت -->
        <div class="temp-mail-section">
            <div class="temp-mail-title">📧 مواقع البريد المؤقت</div>
            <div class="temp-mail-description">
                احصل على بريد إلكتروني مؤقت مجاني للاستخدام السريع
            </div>
            <div class="temp-mail-links">
                <a href="https://temp-mail.org/en/" target="_blank" class="temp-mail-link">
                    🌟 Temp Mail
                </a>
                <a href="https://www.run2mail.com/en" target="_blank" class="temp-mail-link">
                    ⚡ Run2Mail
                </a>
                <a href="https://tempail.com/" target="_blank" class="temp-mail-link">
                    🚀 Tempail
                </a>
            </div>
        </div>

        <!-- قسم العروض -->
        <?php if (!empty($offers)): ?>
        <div class="offers-section">
            <h2 class="offers-title">🎯 العروض المميزة</h2>
            <div class="offers-grid">
                <?php foreach ($offers as $offer): ?>
                <div class="offer-card">
                    <img src="<?php echo htmlspecialchars($offer['image']); ?>"
                         alt="<?php echo htmlspecialchars($offer['name']); ?>"
                         class="offer-image"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPtit2YXZhNin2YQg2KfZhNi12YjYsdipPC90ZXh0Pjwvc3ZnPg=='">

                    <div class="offer-content">
                        <div class="offer-name"><?php echo htmlspecialchars($offer['name']); ?></div>
                        <a href="<?php echo htmlspecialchars($offer['link']); ?>"
                           target="_blank"
                           class="offer-btn">
                            🔗 مشاهدة العرض
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($registeredUsers)): ?>
        <div class="users-list">
            <h3>المستخدمون المسجلون (<?php echo count($registeredUsers); ?>):</h3>
            <?php foreach ($registeredUsers as $user): ?>
            <div class="user-item">
                <div class="user-name"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                <div class="user-date">تاريخ التسجيل: <?php echo date('Y-m-d H:i:s', strtotime($user['registration_date'])); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- رابط لوحة التحكم -->
        <a href="admin.php" class="admin-link">⚙️ لوحة التحكم</a>
    </div>
</body>
</html>
