<?php
// صفحة تفاصيل استهلاك API
session_start();

// دالة لحساب استهلاك API المفصل
function getDetailedApiUsage() {
    $log_file = 'api_usage.txt';
    if (!file_exists($log_file)) {
        return [];
    }
    
    $logs = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $usage_by_date = [];
    $usage_by_hour = [];
    
    foreach ($logs as $log) {
        $date = substr($log, 0, 10); // YYYY-MM-DD
        $hour = substr($log, 11, 2); // HH
        
        if (!isset($usage_by_date[$date])) {
            $usage_by_date[$date] = 0;
        }
        $usage_by_date[$date]++;
        
        if (!isset($usage_by_hour[$hour])) {
            $usage_by_hour[$hour] = 0;
        }
        $usage_by_hour[$hour]++;
    }
    
    // ترتيب حسب التاريخ
    krsort($usage_by_date);
    ksort($usage_by_hour);
    
    return [
        'by_date' => $usage_by_date,
        'by_hour' => $usage_by_hour,
        'total_logs' => count($logs)
    ];
}

$detailed_usage = getDetailedApiUsage();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل استهلاك API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #28a745;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .chart-container {
            margin: 20px 0;
        }
        
        .chart-bar {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .chart-label {
            width: 100px;
            font-size: 14px;
            color: #666;
        }
        
        .chart-bar-fill {
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            margin: 0 10px;
            position: relative;
        }
        
        .chart-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 تفاصيل استهلاك API</h1>
            <p>تحليل مفصل لاستخدام IP Quality Score API</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="ip_check.php" class="nav-link">🔍 فحص الـ IP</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
            </div>
        </div>
        
        <!-- إحصائيات عامة -->
        <div class="section">
            <h2>📈 الإحصائيات العامة</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $detailed_usage['total_logs']; ?></div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($detailed_usage['by_date']); ?></div>
                    <div class="stat-label">أيام النشاط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $detailed_usage['total_logs'] > 0 ? round($detailed_usage['total_logs'] / max(1, count($detailed_usage['by_date'])), 1) : 0; ?></div>
                    <div class="stat-label">متوسط يومي</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo max(0, 5000 - array_sum(array_slice($detailed_usage['by_date'], 0, 30, true))); ?></div>
                    <div class="stat-label">المتبقي هذا الشهر</div>
                </div>
            </div>
        </div>
        
        <!-- الاستخدام حسب التاريخ -->
        <?php if (!empty($detailed_usage['by_date'])): ?>
        <div class="section">
            <h2>📅 الاستخدام حسب التاريخ (آخر 30 يوم)</h2>
            <div class="chart-container">
                <?php 
                $max_daily = max($detailed_usage['by_date']);
                $recent_dates = array_slice($detailed_usage['by_date'], 0, 30, true);
                foreach ($recent_dates as $date => $count): 
                    $percentage = $max_daily > 0 ? ($count / $max_daily) * 100 : 0;
                ?>
                <div class="chart-bar">
                    <div class="chart-label"><?php echo $date; ?></div>
                    <div class="chart-bar-fill" style="width: <?php echo $percentage; ?>%;"></div>
                    <div class="chart-value"><?php echo $count; ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- الاستخدام حسب الساعة -->
        <?php if (!empty($detailed_usage['by_hour'])): ?>
        <div class="section">
            <h2>🕐 الاستخدام حسب الساعة</h2>
            <div class="chart-container">
                <?php 
                $max_hourly = max($detailed_usage['by_hour']);
                for ($hour = 0; $hour < 24; $hour++): 
                    $hour_str = sprintf('%02d', $hour);
                    $count = $detailed_usage['by_hour'][$hour_str] ?? 0;
                    $percentage = $max_hourly > 0 ? ($count / $max_hourly) * 100 : 0;
                ?>
                <div class="chart-bar">
                    <div class="chart-label"><?php echo $hour_str; ?>:00</div>
                    <div class="chart-bar-fill" style="width: <?php echo $percentage; ?>%;"></div>
                    <div class="chart-value"><?php echo $count; ?></div>
                </div>
                <?php endfor; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- جدول تفصيلي -->
        <?php if (!empty($detailed_usage['by_date'])): ?>
        <div class="section">
            <h2>📋 جدول تفصيلي</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>عدد الطلبات</th>
                        <th>النسبة من الإجمالي</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (array_slice($detailed_usage['by_date'], 0, 50, true) as $date => $count): 
                        $percentage = $detailed_usage['total_logs'] > 0 ? round(($count / $detailed_usage['total_logs']) * 100, 1) : 0;
                        $is_today = $date === date('Y-m-d');
                        $is_high = $count > 100;
                    ?>
                    <tr style="<?php echo $is_today ? 'background: #e8f5e8;' : ''; ?>">
                        <td><?php echo $date; ?> <?php echo $is_today ? '(اليوم)' : ''; ?></td>
                        <td><?php echo $count; ?></td>
                        <td><?php echo $percentage; ?>%</td>
                        <td>
                            <?php if ($is_high): ?>
                                <span style="color: #dc3545;">🔴 عالي</span>
                            <?php elseif ($count > 50): ?>
                                <span style="color: #ffc107;">🟡 متوسط</span>
                            <?php else: ?>
                                <span style="color: #28a745;">🟢 عادي</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
