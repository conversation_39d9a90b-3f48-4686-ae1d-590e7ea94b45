<?php
session_start();
require_once 'config.php';

// معرف CPAlead API
$cpalead_id = '1941213';

// معالجة النموذج
$message = '';
$api_offers = [];
$selected_offers = [];

// جلب العروض من API
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fetch_offers'])) {
    $country = cleanInput($_POST['country'] ?? 'user');
    $device = cleanInput($_POST['device'] ?? 'user');
    $limit = (int)($_POST['limit'] ?? 50);
    $offer_type = cleanInput($_POST['offer_type'] ?? '');
    
    // بناء رابط API
    $api_url = "https://www.cpalead.com/api/offers?id=$cpalead_id&country=$country&device=$device&limit=$limit";
    if (!empty($offer_type)) {
        $api_url .= "&type=$offer_type";
    }
    
    // جلب البيانات من API
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $response = @file_get_contents($api_url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['status']) && $data['status'] === 'success') {
            $api_offers = $data['offers'] ?? [];
            $message = '<div class="success">تم جلب ' . count($api_offers) . ' عرض من API بنجاح! ✅</div>';
        } else {
            $message = '<div class="error">فشل في جلب العروض من API. تحقق من الاتصال.</div>';
        }
    } else {
        $message = '<div class="error">خطأ في الاتصال بـ API. يرجى المحاولة مرة أخرى.</div>';
    }
}

// إضافة العروض المختارة إلى قاعدة البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_selected_offers'])) {
    $selected_offer_ids = $_POST['selected_offers'] ?? [];
    $api_offers_data = json_decode($_POST['api_offers_data'] ?? '[]', true);
    
    $added_count = 0;
    foreach ($selected_offer_ids as $offer_id) {
        foreach ($api_offers_data as $offer) {
            if ($offer['id'] == $offer_id) {
                $offer_name = $offer['title'];
                $offer_link = $offer['link'];
                $offer_image = $offer['creatives']['url'] ?? 'https://via.placeholder.com/300x200?text=No+Image';
                $offer_description = $offer['description'] ?? '';
                $offer_terms = $offer['conversion'] ?? '';
                $offer_countries = isset($offer['countries']) ? implode(', ', $offer['countries']) : '';

                if (insertOffer($pdo, $offer_name, $offer_link, $offer_image, $offer_description, $offer_terms, $offer_countries)) {
                    $added_count++;
                }
                break;
            }
        }
    }
    
    if ($added_count > 0) {
        $message = '<div class="success">تم إضافة ' . $added_count . ' عرض إلى الموقع بنجاح! 🎉</div>';
    } else {
        $message = '<div class="error">لم يتم إضافة أي عروض. يرجى المحاولة مرة أخرى.</div>';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض من API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        select, input[type="number"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        select:focus, input[type="number"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .offer-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }
        
        .offer-card:hover {
            transform: translateY(-5px);
        }
        
        .offer-card.selected {
            border-color: #28a745;
            background: #f8fff8;
        }
        
        .offer-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .offer-content {
            padding: 20px;
        }
        
        .offer-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .offer-details {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .offer-amount {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .offer-checkbox {
            margin-bottom: 15px;
        }
        
        .offer-checkbox input[type="checkbox"] {
            margin-left: 8px;
            transform: scale(1.2);
        }
        
        .selected-count {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            margin-bottom: 20px;
            display: inline-block;
        }

        .home-link {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            font-size: 16px;
        }

        .home-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 إدارة العروض من API</h1>
            <p>جلب العروض من CPAlead API وإضافتها للموقع</p>
            <div class="nav-links">
                <a href="index.php" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="admin.php" class="nav-link">⚙️ لوحة التحكم</a>
                <a href="api_offers.php" class="nav-link">🌐 عروض API</a>
                <a href="manage_offers.php" class="nav-link">🗂️ إدارة العروض</a>
                <a href="ip_check.php" class="nav-link">🔍 فحص الـ IP</a>
            </div>
        </div>
        
        <div class="form-section">
            <h2>🔍 جلب العروض من API</h2>
            
            <?php echo $message; ?>
            
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="country">الدولة:</label>
                        <select id="country" name="country">
                            <option value="user">تلقائي (دولة المستخدم)</option>
                            <optgroup label="أمريكا الشمالية">
                                <option value="US">🇺🇸 الولايات المتحدة</option>
                                <option value="CA">🇨🇦 كندا</option>
                                <option value="MX">🇲🇽 المكسيك</option>
                            </optgroup>
                            <optgroup label="أوروبا">
                                <option value="GB">🇬🇧 المملكة المتحدة</option>
                                <option value="DE">🇩🇪 ألمانيا</option>
                                <option value="FR">🇫🇷 فرنسا</option>
                                <option value="IT">🇮🇹 إيطاليا</option>
                                <option value="ES">🇪🇸 إسبانيا</option>
                                <option value="NL">🇳🇱 هولندا</option>
                                <option value="BE">🇧🇪 بلجيكا</option>
                                <option value="CH">🇨🇭 سويسرا</option>
                                <option value="AT">🇦🇹 النمسا</option>
                                <option value="SE">🇸🇪 السويد</option>
                                <option value="NO">🇳🇴 النرويج</option>
                                <option value="DK">🇩🇰 الدنمارك</option>
                                <option value="FI">🇫🇮 فنلندا</option>
                                <option value="IE">🇮🇪 أيرلندا</option>
                                <option value="PT">🇵🇹 البرتغال</option>
                                <option value="GR">🇬🇷 اليونان</option>
                                <option value="PL">🇵🇱 بولندا</option>
                                <option value="CZ">🇨🇿 التشيك</option>
                                <option value="HU">🇭🇺 المجر</option>
                                <option value="RO">🇷🇴 رومانيا</option>
                                <option value="BG">🇧🇬 بلغاريا</option>
                                <option value="HR">🇭🇷 كرواتيا</option>
                                <option value="SI">🇸🇮 سلوفينيا</option>
                                <option value="SK">🇸🇰 سلوفاكيا</option>
                                <option value="EE">🇪🇪 إستونيا</option>
                                <option value="LV">🇱🇻 لاتفيا</option>
                                <option value="LT">🇱🇹 ليتوانيا</option>
                            </optgroup>
                            <optgroup label="آسيا والمحيط الهادئ">
                                <option value="AU">🇦🇺 أستراليا</option>
                                <option value="NZ">🇳🇿 نيوزيلندا</option>
                                <option value="JP">🇯🇵 اليابان</option>
                                <option value="KR">🇰🇷 كوريا الجنوبية</option>
                                <option value="SG">🇸🇬 سنغافورة</option>
                                <option value="HK">🇭🇰 هونغ كونغ</option>
                                <option value="TW">🇹🇼 تايوان</option>
                                <option value="MY">🇲🇾 ماليزيا</option>
                                <option value="TH">🇹🇭 تايلاند</option>
                                <option value="PH">🇵🇭 الفلبين</option>
                                <option value="ID">🇮🇩 إندونيسيا</option>
                                <option value="VN">🇻🇳 فيتنام</option>
                                <option value="IN">🇮🇳 الهند</option>
                                <option value="CN">🇨🇳 الصين</option>
                            </optgroup>
                            <optgroup label="الشرق الأوسط وأفريقيا">
                                <option value="AE">🇦🇪 الإمارات العربية المتحدة</option>
                                <option value="SA">🇸🇦 السعودية</option>
                                <option value="QA">🇶🇦 قطر</option>
                                <option value="KW">🇰🇼 الكويت</option>
                                <option value="BH">🇧🇭 البحرين</option>
                                <option value="OM">🇴🇲 عمان</option>
                                <option value="JO">🇯🇴 الأردن</option>
                                <option value="LB">🇱🇧 لبنان</option>
                                <option value="EG">🇪🇬 مصر</option>
                                <option value="MA">🇲🇦 المغرب</option>
                                <option value="TN">🇹🇳 تونس</option>
                                <option value="DZ">🇩🇿 الجزائر</option>
                                <option value="IL">🇮🇱 إسرائيل</option>
                                <option value="TR">🇹🇷 تركيا</option>
                                <option value="ZA">🇿🇦 جنوب أفريقيا</option>
                                <option value="NG">🇳🇬 نيجيريا</option>
                                <option value="KE">🇰🇪 كينيا</option>
                                <option value="GH">🇬🇭 غانا</option>
                            </optgroup>
                            <optgroup label="أمريكا الجنوبية">
                                <option value="BR">🇧🇷 البرازيل</option>
                                <option value="AR">🇦🇷 الأرجنتين</option>
                                <option value="CL">🇨🇱 تشيلي</option>
                                <option value="CO">🇨🇴 كولومبيا</option>
                                <option value="PE">🇵🇪 بيرو</option>
                                <option value="VE">🇻🇪 فنزويلا</option>
                                <option value="UY">🇺🇾 أوروغواي</option>
                                <option value="EC">🇪🇨 الإكوادور</option>
                            </optgroup>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="device">نوع الجهاز:</label>
                        <select id="device" name="device">
                            <option value="user">تلقائي (جهاز المستخدم)</option>
                            <option value="desktop">سطح المكتب</option>
                            <option value="mobile">الجوال</option>
                            <option value="ios">iOS</option>
                            <option value="android">Android</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="offer_type">نوع العرض:</label>
                        <select id="offer_type" name="offer_type">
                            <option value="">جميع الأنواع</option>
                            <option value="cpi">CPI (تثبيت التطبيق)</option>
                            <option value="cpe">CPE (مشاركة التطبيق)</option>
                            <option value="cpa">CPA (إجراء محدد)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="limit">عدد العروض:</label>
                        <input type="number" id="limit" name="limit" value="50" min="1" max="250">
                    </div>
                </div>
                
                <button type="submit" name="fetch_offers">🔍 جلب العروض من API</button>
            </form>
        </div>
        
        <?php if (!empty($api_offers)): ?>
        <div class="form-section">
            <h2>📋 العروض المتاحة من API</h2>
            
            <form method="POST" id="offersForm">
                <div class="selected-count" id="selectedCount">تم اختيار 0 عرض</div>
                
                <button type="submit" name="add_selected_offers" id="addButton" disabled>
                    ➕ إضافة العروض المختارة للموقع
                </button>
                
                <input type="hidden" name="api_offers_data" value="<?php echo htmlspecialchars(json_encode($api_offers)); ?>">
                
                <div class="offers-grid">
                    <?php foreach ($api_offers as $offer): ?>
                    <div class="offer-card" data-offer-id="<?php echo $offer['id']; ?>">
                        <img src="<?php echo htmlspecialchars($offer['creatives']['url'] ?? 'https://via.placeholder.com/350x200?text=No+Image'); ?>" 
                             alt="<?php echo htmlspecialchars($offer['title']); ?>" 
                             class="offer-image"
                             onerror="this.src='https://via.placeholder.com/350x200?text=No+Image'">
                        
                        <div class="offer-content">
                            <div class="offer-checkbox">
                                <label>
                                    <input type="checkbox" name="selected_offers[]" value="<?php echo $offer['id']; ?>" onchange="updateSelection()">
                                    اختيار هذا العرض
                                </label>
                            </div>
                            
                            <div class="offer-title"><?php echo htmlspecialchars($offer['title']); ?></div>
                            
                            <div class="offer-amount">💰 $<?php echo number_format($offer['amount'], 2); ?> <?php echo $offer['payout_type']; ?></div>
                            
                            <div class="offer-details">
                                <strong>الجهاز:</strong> <?php echo ucfirst($offer['device']); ?><br>
                                <strong>الدول:</strong> <?php echo implode(', ', array_slice($offer['countries'], 0, 5)); ?><?php echo count($offer['countries']) > 5 ? '...' : ''; ?><br>
                                <strong>التحويل:</strong> <?php echo htmlspecialchars(substr($offer['conversion'], 0, 100)); ?><?php echo strlen($offer['conversion']) > 100 ? '...' : ''; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </form>
        </div>
        <?php endif; ?>
    </div>

    <!-- زر عائم للصفحة الرئيسية -->
    <a href="index.php" class="home-link">🏠 الرئيسية</a>

    <script>
        function updateSelection() {
            const checkboxes = document.querySelectorAll('input[name="selected_offers[]"]:checked');
            const count = checkboxes.length;
            const countElement = document.getElementById('selectedCount');
            const addButton = document.getElementById('addButton');
            
            countElement.textContent = `تم اختيار ${count} عرض`;
            addButton.disabled = count === 0;
            
            // تحديث مظهر البطاقات المختارة
            document.querySelectorAll('.offer-card').forEach(card => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                if (checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        }
    </script>
</body>
</html>
